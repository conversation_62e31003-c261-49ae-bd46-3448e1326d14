from app.webhook import handle_webhook, verify_webhook
from app.frontend_handler import (
    index, get_both_reports, update_both, get_solar_reports,
    update_solar, get_dgr_reports, update_dgr_wind, serve_pdf,
    login, logout, api_regenerate, get_status_counts
)

def setup_routes(app):
    app.add_url_rule('/webhook', 'handle_webhook', handle_webhook, methods=['POST'])
    app.add_url_rule('/webhook', 'verify_webhook', verify_webhook, methods=['GET'])
    app.add_url_rule('/api/both', 'get_both_reports', get_both_reports, methods=['GET'])
    app.add_url_rule('/api/both/update', 'update_both', update_both, methods=['POST'])
    app.add_url_rule('/api/solar', 'get_solar_reports', get_solar_reports, methods=['GET'])
    app.add_url_rule('/api/solar/update', 'update_solar', update_solar, methods=['POST'])
    app.add_url_rule('/api/wind', 'get_dgr_reports', get_dgr_reports, methods=['GET'])
    app.add_url_rule('/api/wind/update', 'update_dgr_wind', update_dgr_wind, methods=['POST'])
    app.add_url_rule('/', 'index', index)
    app.add_url_rule('/api/pdf/<report_type>/<int:report_id>', 'serve_pdf', serve_pdf)
    app.add_url_rule('/login', 'login', login, methods=['GET', 'POST'])
    app.add_url_rule('/logout', 'logout', logout)
    app.add_url_rule('/api/regenerate', 'api_regenerate', api_regenerate, methods=['POST'])
    app.add_url_rule('/api/status_counts', 'get_status_counts', get_status_counts, methods=['GET'])
