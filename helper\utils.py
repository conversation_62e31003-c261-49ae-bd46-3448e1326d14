import pandas as pd
import time
import csv
from config.settings import Config
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, PageBreak
from helper.integration_utilities import PrescintoIntegrationUtilities
from PyPDF2 import PdfMerger
from helper.logger_setup import setup_logger
import os
import traceback
from dotenv import load_dotenv
from datetime import datetime, timedelta
from collections import defaultdict
from dateutil.relativedelta import relativedelta



load_dotenv()

# Setup logger
logging = setup_logger('utils', 'utils.log')


API_TOKEN =os.getenv('API_TOKEN')

# Initialize Prescinto Integration
m = PrescintoIntegrationUtilities(server='IN', token=API_TOKEN)

def fetch_data(plant_name, params, category, start_date, end_date, retries=3):
    """
    Fetches data from Prescinto API with error handling and retries.
    """
    for attempt in range(retries):
        try:
            data = m.fetchDataV2(plant_name, category, params, None, start_date, end_date, granularity='15m')
            if isinstance(data, str):  # API returned an error message as a string
                logging.error(f"Error fetching {params} (Attempt {attempt+1}): {data}")
                continue
            df = pd.DataFrame(data)
            if not df.empty:
                return df
        except Exception as e:
            logging.error(f"Exception fetching {params} (Attempt {attempt+1}): {e}")
        time.sleep(5)  # Wait before retrying
    return pd.DataFrame()


def generate_solar_dgr_report(poa, pr, total_generation, start_date, customer_name, project):
    # Ensure missing values are replaced with zero
    poa = poa if poa not in [None, ""] else 0
    pr = pr if pr not in [None, ""] else 0
    total_generation = total_generation if total_generation not in [None, ""] else 0

    output_file = f"Solar_DGR_Report_{start_date}.csv"
    
    try:
        with open(output_file, mode="w", newline="") as file:
            writer = csv.writer(file)
            writer.writerow(["Date", "Customer Name", "Project", "POA(w/m2)", "PR%", "Daily Generation(kwh)"])
            writer.writerow([start_date, customer_name, project, round(float(poa), 2), round(float(pr), 2), round(float(total_generation), 2)])
        
        return output_file

    except Exception as e:
        logging.error(f"Error generating CSV report: {e}")
        return None



def generate_solar_dgr_pdf(date, customer_name, poa, pr, daily_generation, month_gen_value, month_pr_value, monthly_poa, capacity, output_file):
    
    try:
        # Create PDF
        doc = SimpleDocTemplate(output_file, pagesize=A4)
        elements = []
        styles = getSampleStyleSheet()

        # Title
        title_name = f"<b><font size=16>{customer_name} - DAILY GENERATION REPORT<br/>Capacity: {capacity} MW | Date: {date}</font></b>"

        title = Paragraph(title_name, styles["Title"])
        elements.append(title)
        elements.append(Spacer(1, 12))

        # Data Table
        data = [
            [Paragraph("<b>Daily POA (kW/m2):</b>", styles["BodyText"]), round(float(poa / 1000), 2)],
            [Paragraph("<b>Daily PR%:</b>", styles["BodyText"]), round(float(pr), 1)],
            [Paragraph("<b>Daily Generation (kWh):</b>", styles["BodyText"]), round(float(daily_generation))],
            [Paragraph("<b>Monthly PR%:</b>", styles["BodyText"]), round(month_pr_value, 1)],
            [Paragraph("<b>Monthly Generation (kWh):</b>", styles["BodyText"]), round(float(month_gen_value))],
            [Paragraph("<b>Monthly POA (kW/m2):</b>", styles["BodyText"]), round(float(monthly_poa / 1000), 2)]
        ]

        # Table Styling
        table = Table(data, colWidths=[180, 250])
        table.setStyle(TableStyle([
            ('BOX', (0, 0), (-1, -1), 2, colors.black),
            ('GRID', (0, 0), (-1, -1), 1, colors.grey),
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('FONTNAME', (0, 0), (-1, -1), "Helvetica"),
            ('FONTSIZE', (0, 0), (-1, -1), 11),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('LEFTPADDING', (0, 0), (-1, -1), 10),
            ('RIGHTPADDING', (0, 0), (-1, -1), 10),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))

        elements.append(table)
        doc.build(elements)
        logging.info(f"PDF generated: {output_file}")
        return output_file

    except Exception as e:
        logging.error(f"Error generating Solar DGR PDF: {e}")
        return None



def generate_combined_wind_pdf(plant_name, start_date, customer_name, project, avg_wind_speed, total_generation, 
                               ma_percent, monthly_wind, monthly_generation, 
                               csv_filename, capacity, output_file):
    # output_file = f"{plant_name}_Wind_DGR_Combined.pdf"
    
    try:
        doc = SimpleDocTemplate(output_file, pagesize=A4)
        elements = []
        styles = getSampleStyleSheet()
        
        # === Title Section ===
        title_name = f"<b><font size=16>{customer_name} - DAILY GENERATION REPORT<br/>Capacity: {capacity} MW | Date: {start_date}</font></b>"
        title = Paragraph(title_name, styles["Title"])
        elements.append(title)
        elements.append(Spacer(1, 12))

        # === Summary Table ===
        summary_data = [
            [Paragraph("<b>Daily Avg Wind Speed (m/s):</b>", styles["BodyText"]), round(float(avg_wind_speed), 2)],
            [Paragraph("<b>Daily Generation (kWh):</b>", styles["BodyText"]), round(float(total_generation))],
            [Paragraph("<b>Monthly Wind Speed (m/s):</b>", styles["BodyText"]), round(monthly_wind, 2)],
            [Paragraph("<b>Monthly Generation (kWh):</b>", styles["BodyText"]), round(float(monthly_generation))],
        ]

        summary_table = Table(summary_data, colWidths=[180, 250])
        summary_table.setStyle(TableStyle([
            ('BOX', (0, 0), (-1, -1), 2, colors.black),
            ('GRID', (0, 0), (-1, -1), 1, colors.grey),
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('FONTNAME', (0, 0), (-1, -1), "Helvetica"),
            ('FONTSIZE', (0, 0), (-1, -1), 11),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('LEFTPADDING', (0, 0), (-1, -1), 10),
            ('RIGHTPADDING', (0, 0), (-1, -1), 10),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))

        elements.append(summary_table)
        elements.append(Spacer(1, 20))

        # === CSV Data Section ===
        if csv_filename:
            df = pd.read_csv(csv_filename)
            if not df.empty:
                data = [df.columns.tolist()] + df.values.tolist()
                
                # Extract location numbers
                loc_nos = [col.replace(".Wind-Speed", "") for col in df.columns if "Wind-Speed" in col]
                
                # Define the table styling
                table = Table(data)
                table.setStyle(TableStyle([
                    # Header row styling
                    ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#2C3E50')),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.HexColor('#FFFFFF')),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),

                    # Data rows styling
                    ('BACKGROUND', (0, 1), (-1, -1), colors.HexColor('#F4F6F7')),
                    ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
                    ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                    ('ALIGN', (0, 1), (-1, -1), 'CENTER'),
                    ('BOTTOMPADDING', (0, 1), (-1, -1), 8),

                    # Alternating row colors
                    ('BACKGROUND', (0, 1), (-1, -1), colors.HexColor('#ECF0F1')),
                    ('BACKGROUND', (0, 2), (-1, -1), colors.HexColor('#FFFFFF')),

                    # Gridlines
                    ('GRID', (0, 0), (-1, -1), 0.5, colors.HexColor('#BDC3C7')),
                ]))

                if len(loc_nos) > 10:
                    # If more than 10 locations, split into multiple pages
                    elements.append(PageBreak())
                    elements.append(table)
                else:
                    # If 10 or fewer locations, keep on the same page
                    elements.append(table)

        # === Build the PDF ===
        doc.build(elements)
        logging.info(f"Combined Wind DGR PDF generated successfully: {output_file}")
        return output_file

    except Exception as e:
        logging.error(f"Error generating combined Wind DGR PDF: {e}", exc_info=True)
        return None



def generate_dgr_wind_report(plant_name, wind_speed_data, daily_generation, start_date, customer_name, project, ma_percent):
    output_file = f"{plant_name}Wind_DGR_Report_{start_date}.csv"

    try:
        if wind_speed_data.empty or daily_generation.empty:
            # If there's no data, create a single row with zeros
            processed_data = [{
                "Loc No": 0,
                "Avg Wind Speed": 0,
                "Daily Generation (KWh)": 0,
                "MA%": 0
            }]
        else:
            # Merge with daily_generation (left join to retain all wind speed data)
            merged_data = pd.merge(wind_speed_data, daily_generation, on="time", how="left").fillna(0)

            # Extract location numbers
            loc_nos = [col.replace(".Wind-Speed", "") for col in wind_speed_data.columns if col != "time"]

            # Process data
            processed_data = [
                {
                    "Loc No": loc_no,
                    "Avg Wind Speed": round(merged_data[f"{loc_no}.Wind-Speed"].mean(), 2),
                    "Daily Generation (KWh)": round(merged_data.get(f"{loc_no}.Generation today", pd.Series(0)).sum()),
        
                }
                for loc_no in loc_nos
            ]

        # Create DataFrame and save to CSV
        df = pd.DataFrame(processed_data)
        df.to_csv(output_file, index=False)
        return output_file

    except Exception as e:
        logging.error(f"Error generating CSV report: {e}")
        return None



def generate_dgr_wind_pdf(plant_name, start_date, customer_name, project, avg_wind_speed, total_generation, ma_percent,
            monthly_wind, monthly_generation, yearly_wind, yearly_generation):
    output_file = f"Wind_DGR_Summary.pdf"
    try:
        capacity = "10"
        doc = SimpleDocTemplate(output_file, pagesize=A4)
        elements = []
        styles = getSampleStyleSheet()

        # Title without customer, project, and date
        title_name = f"<b><font size=16>{customer_name} - DAILY GENERATION REPORT<br/>Capacity: {capacity} MW | Date: {start_date}</font></b>"

        title = Paragraph(title_name, styles["Title"])
        elements.append(title)
        elements.append(Spacer(1, 12))

        # Cleaned up data table (Removed ma_percent)
        data = [
            [Paragraph("<b>Daily Avg Wind Speed (m/s):</b>", styles["BodyText"]), round(float(avg_wind_speed), 2)],
            [Paragraph("<b>Daily Generation (kWh):</b>", styles["BodyText"]), round(float(total_generation))],
            [Paragraph("<b>Monthly Wind Speed (m/s):</b>", styles["BodyText"]), round(monthly_wind, 2)],
            [Paragraph("<b>Monthly Generation (kWh):</b>", styles["BodyText"]), round(float(monthly_generation))],
        ]

        # Table styling
        table = Table(data, colWidths=[180, 250])
        table.setStyle(TableStyle([
            ('BOX', (0, 0), (-1, -1), 2, colors.black),
            ('GRID', (0, 0), (-1, -1), 1, colors.grey),
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('FONTNAME', (0, 0), (-1, -1), "Helvetica"),
            ('FONTSIZE', (0, 0), (-1, -1), 11),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('LEFTPADDING', (0, 0), (-1, -1), 10),
            ('RIGHTPADDING', (0, 0), (-1, -1), 10),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))

        elements.append(table)
        doc.build(elements)
        return output_file

    except Exception as e:
        logging.error(f"Error generating Wind DGR PDF: {e}")
        return None

    

def get_monthly_yearly_dates(endDate: str):
    try:
        # Convert string to datetime object
        end_date = datetime.strptime(endDate, '%Y-%m-%d')
        
        # Get the first day of the month
        month_date = end_date.replace(day=1)
        
        # Get the first day of the year
        yearly_date = end_date.replace(month=1, day=1)
        
        return month_date.strftime('%Y-%m-%d'), yearly_date.strftime('%Y-%m-%d')
    except ValueError:
        return "Invalid date format. Please use YYYY-MM-DD."



def csv_to_pdf(csv_filename):
    """
    Converts a CSV file to a professionally styled PDF document.

    Args:
        csv_filename (str): Path to the input CSV file.

    Returns:
        str: Path to the generated PDF file, or None if conversion fails.
    """
    if not csv_filename:
        logging.error("CSV to PDF conversion skipped due to missing CSV file.")
        return None

    # Generate PDF filename by replacing .csv with .pdf
    pdf_filename = csv_filename.replace(".csv", ".pdf")
    
    try:
        # Read the CSV file into a DataFrame
        df = pd.read_csv(csv_filename)

        # Convert DataFrame to a list format for the table
        data = [df.columns.tolist()] + df.values.tolist()

        # Create a PDF document
        doc = SimpleDocTemplate(pdf_filename, pagesize=A4)
        elements = []

        # Define the table with professional styling
        table = Table(data)
        table.setStyle(TableStyle([
            # Header row styling
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#2C3E50')),  # Dark Blue
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.HexColor('#FFFFFF')),   # White
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),

            # Data rows styling
            ('BACKGROUND', (0, 1), (-1, -1), colors.HexColor('#F4F6F7')),  # Light Grey
            ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('ALIGN', (0, 1), (-1, -1), 'CENTER'),
            ('BOTTOMPADDING', (0, 1), (-1, -1), 8),

            # Alternating row colors for better readability
            ('BACKGROUND', (0, 1), (-1, -1), colors.HexColor('#ECF0F1')),  # Light Grey for alternate rows
            ('BACKGROUND', (0, 2), (-1, -1), colors.HexColor('#FFFFFF')),  # White for other rows

            # Gridlines for the table
            ('GRID', (0, 0), (-1, -1), 0.5, colors.HexColor('#BDC3C7'))   # Soft Grey Grid
        ]))

        # Add the table to the PDF elements
        elements.append(table)
        doc.build(elements)
        
        logging.info(f"CSV converted to PDF successfully: {pdf_filename}")
        return pdf_filename

    except Exception as e:
        logging.error(f"Error converting CSV to PDF: {e}", exc_info=True)
        return None


def merge_pdfs(final_plot_path, pdf1, output_filename):
    if not pdf1 or not final_plot_path:
        logging.error("Skipping PDF merging due to missing files.")
        return None

    try:
        merger = PdfMerger()
        merger.append(final_plot_path)
        merger.append(pdf1)
        # merger.append(pdf2)
        merger.write(output_filename)
        merger.close()
        return output_filename
    except Exception as e:
        logging.error(f"Error merging PDFs: {e}")
        return None


def merge_pdfs_wind(pdf1, pdf2, output_filename):
    if not pdf1 or not pdf2:
        logging.error("Skipping PDF merging due to missing files.")
        return None

    try:
        merger = PdfMerger()
        merger.append(pdf1)
        merger.append(pdf2)
        merger.write(output_filename)
        merger.close()
        return output_filename
    except Exception as e:
        logging.error(f"Error merging PDFs: {e}")
        return None



def fetch_data_total(plant_name, params, category, start_date, end_date, condition, retries=3):
    """
    Fetches data from Prescinto API with error handling and retries.
    """
    for attempt in range(retries):
        try:
            data = m.fetchDataV2(plant_name, category, params, None, start_date, end_date, granularity='1d', condition=condition)
            if isinstance(data, str):  # API returned an error message as a string
                logging.error(f"Error fetching fetch_data_total for plant - {plant_name} - {params} (Attempt {attempt+1}): {data}")
                continue
            df = pd.DataFrame(data)
            if not df.empty:
                return df
        except Exception as e:
            logging.error(f"Exception fetching fetch_data_total for plant - {plant_name} -  {params} (Attempt {attempt+1}): {e}")
        time.sleep(5)  # Wait before retrying
    return pd.DataFrame()



def get_dynamic_dates(end_date_str):
    """
    Given an end date (YYYY-MM-DD), this function calculates:
    - Start and end dates of last month
    - Start and end dates of last year
    - Start date of the current month
    - Start date of the last 30 days (including 1 extra day)
    - Start date of the current year

    :param end_date_str: String format of the end date (e.g., '2025-03-06')
    :return: Tuple (current_month_start, last_month_start, last_month_end, last_year_start, last_year_end, last_30_days_start, current_year_start)
    """
    end_date = datetime.strptime(end_date_str, "%Y-%m-%d")  # Convert string to date
    
    # Start date of the given month
    current_month_start = end_date.replace(day=1)
    
    # Last month start and end dates
    last_month_start = (end_date.replace(day=1) - relativedelta(months=1))
    last_month_end = (end_date.replace(day=1) - timedelta(days=1))

    # Last year start and end dates
    last_year_start = end_date.replace(year=end_date.year - 1)
    last_year_end = last_year_start.replace(month=12, day=31)
    
    # Start date for last 30 days (including one extra day)
    last_30_days_start = end_date - timedelta(days=30)

    # **Start date of the current year**
    current_year_start = end_date.replace(month=1, day=1)

    last_year_date = end_date - relativedelta(years=1)

    return (current_month_start.strftime("%Y-%m-%d"),
            last_month_start.strftime("%Y-%m-%d"),
            last_month_end.strftime("%Y-%m-%d"),
            last_year_start.strftime("%Y-%m-%d"),
            last_year_end.strftime("%Y-%m-%d"),
            last_30_days_start.strftime("%Y-%m-%d"),
            current_year_start.strftime("%Y-%m-%d"),
            last_year_date.strftime("%Y-%m-%d"))

def get_capacity_from_csv(plant_id):
    csv_path = Config.CUSTOMER_DATA_CSV_PATH
    return next((row['Capacity ( MW)'].strip() for row in csv.DictReader(open(csv_path, encoding='utf-8')) if row['Plant id'].strip() == plant_id), "N/A")

def generate_combined_both_pdf(
    date, customer_name, project, 
    daily_poa, daily_pr, daily_gen_solar, month_gen_solar, month_pr_solar, 
    monthly_poa_solar,
    daily_wind_speed, daily_gen_wind, monthly_wind, monthly_gen_wind,
    wind_capacity, solar_capacity,
    wind_speed_data, daily_generation,
    plant_name_solar, plant_name_wind, output_file
):
    """
    Generates a combined DGR (Daily Generation Report) PDF for Solar and Wind energy.
    Includes details like POA, PR%, daily/monthly generation, and location-wise wind data.

    :param date: Report date (YYYY-MM-DD)
    :param customer_name: Customer/Company name
    :param project: Project name
    :param daily_poa: Daily POA (kW/m²)
    :param daily_pr: Daily Performance Ratio (%)
    :param daily_gen_solar: Daily Solar Generation (kWh)
    :param month_gen_solar: Monthly Solar Generation (kWh)
    :param month_pr_solar: Monthly PR% for Solar
    :param monthly_poa_solar: Monthly POA (kW/m²)
    :param daily_wind_speed: Daily average wind speed (m/s)
    :param daily_gen_wind: Daily Wind Generation (kWh)
    :param monthly_wind: Monthly average wind speed (m/s)
    :param monthly_gen_wind: Monthly Wind Generation (kWh)
    :param wind_capacity: Wind plant capacity (MW)
    :param solar_capacity: Solar plant capacity (MW)
    :param wind_speed_data: DataFrame containing wind speed readings
    :param daily_generation: DataFrame containing daily generation data
    :param ma_percent: Machine Availability Percentage (MA%)

    :return: Full file path of the generated PDF or None in case of error
    """
    try:
        # Determine Current Month End
        report_date = datetime.strptime(date, "%Y-%m-%d")
        current_month_end = (report_date.replace(day=1) + relativedelta(months=1)) - timedelta(days=1)

        # Define PDF file name
        # output_file = f"{plant_name_solar}_Combined_DGR_Summary_{date}.pdf"

        # Initialize Document
        doc = SimpleDocTemplate(output_file, pagesize=A4)
        elements = []
        styles = getSampleStyleSheet()

        # Title Section
        title_text = (
            f"<b><font size=16>{customer_name} - DAILY GENERATION REPORT<br/>"  f"| Date: {date}</font></b>"
        )
        elements.append(Paragraph(title_text, styles["Title"]))
        elements.append(Spacer(1, 12))

        # Solar Data Table
        solar_data = [
            [Paragraph("<b>Solar Plant Capacity (MW):</b>", styles["BodyText"]), solar_capacity],
            [Paragraph("<b>Daily POA (kW/m²):</b>", styles["BodyText"]), round(daily_poa / 1000, 2)],
            [Paragraph("<b>Daily PR%:</b>", styles["BodyText"]), round(daily_pr, 1)],
            [Paragraph("<b>Daily Generation (kWh):</b>", styles["BodyText"]), round(daily_gen_solar)],
            [Paragraph("<b>Monthly PR%:</b>", styles["BodyText"]), round(month_pr_solar, 1)],
            [Paragraph("<b>Monthly Generation (kWh):</b>", styles["BodyText"]), round(month_gen_solar)],
            [Paragraph("<b>Monthly POA (kW/m²):</b>", styles["BodyText"]), round(monthly_poa_solar / 1000, 2)],
        ]

        solar_table = Table(solar_data, colWidths=[220, 250])
        solar_table.setStyle(TableStyle([
            ('BOX', (0, 0), (-1, -1), 2, colors.black),
            ('GRID', (0, 0), (-1, -1), 1, colors.grey),
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('FONTNAME', (0, 0), (-1, -1), "Helvetica"),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ]))
        elements.append(Paragraph(f"<b>Solar Generation: {plant_name_solar}</b>", styles["Heading2"]))
        elements.append(solar_table)
        elements.append(Spacer(1, 20))

        # Wind Data Table
        wind_data = [
            [Paragraph("<b>Wind Plant Capacity (MW):</b>", styles["BodyText"]), wind_capacity],
            [Paragraph("<b>Daily Avg Wind Speed (m/s):</b>", styles["BodyText"]), round(daily_wind_speed, 2)],
            [Paragraph("<b>Daily Generation (kWh):</b>", styles["BodyText"]), round(daily_gen_wind)],
            [Paragraph("<b>Monthly Wind Speed (m/s):</b>", styles["BodyText"]), round(monthly_wind, 2)],
            [Paragraph("<b>Monthly Generation (kWh):</b>", styles["BodyText"]), round(monthly_gen_wind)]
           
        ]

        wind_table = Table(wind_data, colWidths=[220, 250])
        wind_table.setStyle(TableStyle([
            ('BOX', (0, 0), (-1, -1), 2, colors.black),
            ('GRID', (0, 0), (-1, -1), 1, colors.grey),
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
        ]))

        elements.append(Paragraph(f"<b>Wind Generation: {plant_name_wind}</b>", styles["Heading2"]))

        elements.append(wind_table)
        elements.append(Spacer(1, 20))

        # Wind Location-Specific Data Table
        if not wind_speed_data.empty and not daily_generation.empty:
            merged_data = pd.merge(wind_speed_data, daily_generation, on="time", how="left").fillna(0)
            loc_nos = [col.replace(".Wind-Speed", "") for col in wind_speed_data.columns if col != "time"]

            wind_loc_data = [[Paragraph("<b>Loc No</b>", styles["BodyText"]), 
                              Paragraph("<b>Avg Wind Speed (m/s)</b>", styles["BodyText"]), 
                              Paragraph("<b>Daily Generation (kWh)</b>", styles["BodyText"])]]

            for loc_no in loc_nos:
                wind_loc_data.append([
                    loc_no,
                    round(merged_data[f"{loc_no}.Wind-Speed"].mean(), 2),
                    round(merged_data.get(f"{loc_no}.Generation today", pd.Series(0)).sum())
                ])

            wind_loc_table = Table(wind_loc_data, colWidths=[100, 160, 160])
            wind_loc_table.setStyle(TableStyle([
                ('BOX', (0, 0), (-1, -1), 2, colors.black),
                ('GRID', (0, 0), (-1, -1), 1, colors.grey),
                ('BACKGROUND', (0, 0), (0, 0), colors.lightgrey),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ]))

            elements.append(Paragraph("<b>Wind Turbine Location-Specific Data</b>", styles["Heading2"]))
            elements.append(wind_loc_table)

        # Build PDF
        doc.build(elements)
        logging.info(f"Combined PDF generated: {output_file}")

        return output_file

    except Exception as e:
        logging.error(f"Error generating Combined DGR PDF: {e}\n{traceback.format_exc()}")
        return None



def get_both_plant_pairs_from_csv(csv_file_path):
    """
    Parses the CSV file and returns a list of (solar_plant_id, wind_plant_id, customer_name)
    tuples for entries that have the same non-empty 'Combined' field (like 'both1', 'both2').
    """
    combined_map = defaultdict(dict)  # key = combined tag, value = {'solar': ..., 'wind': ..., 'customer': ...}
    both_plant_pairs = []

    try:
        with open(csv_file_path, mode='r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                combined = row.get('Combined', '').strip().lower()
                plant_type = row.get('Type', '').strip().lower()
                plant_id = row.get('Plant id', '').strip()
                customer_name = row.get('Customer Name', '').strip()

                if combined and combined.startswith('both') and plant_id and customer_name:
                    if plant_type in ['solar', 'wind']:
                        combined_map[combined][plant_type] = plant_id
                        combined_map[combined]['customer'] = customer_name

        for tag, data in combined_map.items():
            if 'solar' in data and 'wind' in data:
                both_plant_pairs.append((data['solar'], data['wind'], data['customer']))

    except Exception as e:
        logging.error(f"Error reading both plant data from CSV: {e}")

    return both_plant_pairs





def get_data_wind_solar(csv_file_path, plant_type):
    """
    Reads wind plant and customer pairs from the given CSV file.
    Filters:
      - Only rows with Type == 'Wind'
      - Excludes rows where Combined column has any non-empty value
    Returns a list of (plant_id, customer_name) tuples.
    """
    plant_customer_pairs = []

    try:
        with open(csv_file_path, mode='r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                if row['Type'].strip().lower() == plant_type:
                    combined = row.get('Combined', '').strip().lower()
                    if combined:  # Skip if there's any value in Combined
                        continue
                    plant_id = row['Plant id'].strip()
                    customer_name = row['Customer Name'].strip()
                    if plant_id and customer_name:
                        plant_customer_pairs.append((plant_id, customer_name))
    except Exception as e:
        logging.error(f"Error reading CSV file '{csv_file_path}': {e}")

    return plant_customer_pairs



def merge_pdfs_both_plants(summary_pdf, gen_plot_year, wind_plot_year, combined_30, final_plot_path, final_pdf_path):
    
    if not summary_pdf or not gen_plot_year:
        logging.error("Skipping PDF merging due to missing files.")
        return None

    try:
        merger = PdfMerger()
        merger.append(summary_pdf)
        merger.append(gen_plot_year)
        merger.append(wind_plot_year)
        merger.append(combined_30)
        merger.append(final_plot_path)
        merger.write(final_pdf_path)
        merger.close()
        return final_pdf_path
    except Exception as e:
        logging.error(f"Error merging PDFs: {e}")
        return None
    

def get_contact_number_from_csv(plant_id):
    csv_path = Config.CUSTOMER_DATA_CSV_PATH
    # Find the contact numbers, strip any extra spaces, and split by commas
    test_numbers = next(
        (row['Test Number'].strip().split(',') for row in csv.DictReader(open(csv_path, encoding='utf-8')) if row['Plant id'].strip() == plant_id),
        ["N/A"]
    )
    contact_numbers = next(
        (row['Contact Number'].strip().split(',') for row in csv.DictReader(open(csv_path, encoding='utf-8')) if row['Plant id'].strip() == plant_id),
        ["N/A"]
    )
    # Strip spaces from each number in the list
    return [number.strip() for number in test_numbers + contact_numbers]



