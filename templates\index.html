<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>DGR Validation Dashboard</title>
  <link rel="icon" href="{{ url_for('static', filename='logo_integrum.jpg') }}" type="image/jpeg">
  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" />
  <!-- Font Awesome for Icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" />
  <style>
    :root {
      --primary-color: #0d6efd;
      --secondary-color: #6c757d;
      --success-color: #198754;
      --warning-color: #ffc107;
      --danger-color: #dc3545;
      --info-color: #0dcaf0;
      --light-gray: #f8f9fa;
      --medium-gray: #e9ecef;
      --dark-gray: #343a40;
      --body-bg: #f4f7fc;
      --card-bg: #ffffff;
      --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      --border-radius: 0.5rem; /* 8px */
      --font-family-sans-serif: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    body {
      background-color: var(--body-bg);
      font-family: var(--font-family-sans-serif);
      color: var(--dark-gray);
    }

    .navbar {
      background-color: var(--card-bg);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      padding: 0.5rem 1.5rem; /* Reduced padding */
    }

    .navbar-brand img {
      height: 45px; /* Adjusted logo size */
      width: auto;
    }

    .main-content {
      padding-top: 2rem;
      padding-bottom: 3rem;
    }

    h1.page-title {
      font-weight: 600;
      color: var(--dark-gray);
      margin-bottom: 1.5rem;
      text-align: center;
    }

    .card {
      background-color: var(--card-bg);
      border: none;
      border-radius: var(--border-radius);
      box-shadow: var(--card-shadow);
      margin-bottom: 1.5rem;
    }

    .card-header {
      background-color: transparent;
      border-bottom: 1px solid var(--medium-gray);
      padding: 1rem 1.5rem;
      font-weight: 600;
      color: var(--primary-color);
    }

    .card-body {
      padding: 1.5rem;
    }

    .form-label {
      font-weight: 500;
      margin-bottom: 0.5rem;
      font-size: 0.9rem;
      color: var(--secondary-color);
    }

    .form-control, .btn {
      border-radius: 0.375rem; /* 6px */
    }

    .btn-primary {
      background-color: var(--primary-color);
      border-color: var(--primary-color);
      transition: background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;
    }
    .btn-primary:hover {
      background-color: #0b5ed7;
      border-color: #0a58ca;
    }

    /* Status Count Boxes Styling */
    .status-card {
      border-left: 4px solid;
      padding: 0.75rem 1rem; /* Adjusted padding */
      display: flex;
      align-items: center;
      justify-content: space-between;
      background-color: var(--light-gray);
      border-radius: 0.375rem;
      transition: transform 0.2s ease;
    }
    .status-card:hover {
        transform: translateY(-2px);
    }
    .status-card .status-icon {
      font-size: 1.2rem; /* Adjusted icon size */
      margin-right: 0.75rem;
    }
    .status-card .status-text {
      font-weight: 500;
      font-size: 0.85rem; /* Adjusted text size */
      color: var(--secondary-color);
    }
    .status-card .status-count {
      font-size: 1.4rem; /* Adjusted count size */
      font-weight: 600;
    }
    .status-card.border-success { border-color: var(--success-color); }
    .status-card.border-secondary { border-color: var(--secondary-color); }
    .status-card.border-warning { border-color: var(--warning-color); }
    .status-card.border-info { border-color: var(--info-color); }
    .status-card.border-danger { border-color: var(--danger-color); }
    .status-card .text-success { color: var(--success-color) !important; }
    .status-card .text-secondary { color: var(--secondary-color) !important; }
    .status-card .text-warning { color: var(--warning-color) !important; }
    .status-card .text-info { color: var(--info-color) !important; }
    .status-card .text-danger { color: var(--danger-color) !important; }

    /* Tab Styling */
    .nav-pills .nav-link {
      color: var(--secondary-color);
      font-weight: 500;
      border-radius: var(--border-radius);
      padding: 0.6rem 1.2rem;
      transition: background-color 0.2s ease, color 0.2s ease;
    }
    .nav-pills .nav-link.active {
      background-color: var(--primary-color);
      color: white;
      box-shadow: 0 2px 5px rgba(13, 110, 253, 0.3);
    }
    .nav-pills .nav-link:not(.active):hover {
      background-color: var(--medium-gray);
    }

    /* Table Styling */
    .table {
      border-collapse: separate;
      border-spacing: 0;
      margin-bottom: 0; /* Remove default margin */
    }
    .table th, .table td {
      padding: 0.9rem 1rem; /* Increased padding */
      vertical-align: middle;
      font-size: 0.9rem;
      white-space: nowrap;
    }
    .table thead th {
      background-color: var(--light-gray);
      color: var(--dark-gray);
      font-weight: 600;
      border-bottom: 2px solid var(--medium-gray);
      text-align: left;
    }
    .table tbody tr {
      background-color: var(--card-bg);
      transition: background-color 0.15s ease-in-out;
    }
    .table tbody tr:hover {
      background-color: #f1f4f9; /* Subtle hover effect */
    }
    .table-responsive {
      border: 1px solid var(--medium-gray);
      border-radius: var(--border-radius);
      overflow-x: auto; /* Enable horizontal scroll */
      overflow-y: hidden;
      width: 100%;
      /* Ensures border radius applies to table */
    }

    /* Button Styling in Table */
    .table .btn {
      padding: 0.3rem 0.6rem; /* Smaller padding for table buttons */
      font-size: 0.8rem;
      margin-right: 0.3rem;
      min-width: 85px; /* Ensure buttons have minimum width */
      text-align: center;
    }
    .table .btn i {
      margin-right: 0.3rem;
    }
    .table .btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
    .btn-action-group {
        display: flex;
        gap: 0.5rem; /* Space between review/regenerate */
    }

    /* Status Cell Styling */
    .status-cell .badge {
        font-size: 0.8rem;
        padding: 0.4em 0.7em;
    }

    /* Utility Classes */
    .fw-medium { font-weight: 500; }
    .fw-semibold { font-weight: 600; }

  </style>
</head>
<body>
  <!-- Navbar -->
  <nav class="navbar navbar-expand-lg navbar-light">
    <div class="container-fluid">
      <a class="navbar-brand" href="#">
        <img src="{{ url_for('static', filename='logo_integrum.jpg') }}" alt="Integrum Logo" />
      </a>
      <button id="logoutBtn" class="btn btn-outline-danger btn-sm">
        <i class="fas fa-sign-out-alt me-1"></i>Logout
      </button>
    </div>
  </nav>

  <!-- Main Content Area -->
  <div class="container-xl main-content">
    <h1 class="page-title">DGR Validation Dashboard</h1>

    <!-- Filter and Status Card -->
    <div class="card">
      <div class="card-body">
        <div class="row g-3 align-items-end mb-4">
          <div class="col-md-4 col-lg-3">
            <label for="startDateInput" class="form-label">Start Date</label>
            <input type="date" id="startDateInput" class="form-control form-control-sm" />
          </div>
          <div class="col-md-4 col-lg-3">
            <label for="endDateInput" class="form-label">End Date</label>
            <input type="date" id="endDateInput" class="form-control form-control-sm" />
          </div>
          <div class="col-md-2 col-lg-2">
            <button id="filterBtn" class="btn btn-primary btn-sm w-100">
              <i class="fas fa-filter me-1"></i>Filter
            </button>
          </div>
        </div>

        <!-- Status Count Boxes -->
        {% set status_icons = {
          'Sent': 'fa-check-circle',
          'Pending': 'fa-clock',
          'In Review': 'fa-search',
          'Regenerated': 'fa-sync-alt',
          'Not Sent': 'fa-ban'
        } %}
        {% set status_colors = {
          'Sent': 'success',
          'Pending': 'secondary',
          'In Review': 'warning',
          'Regenerated': 'info',
          'Not Sent': 'danger'
        } %}

        {% for type, counts in status_counts.items() %}
          <div id="statusCounts{{ type|capitalize }}" class="row g-2 mt-3" style="display: {% if type == 'both' %}flex{% else %}none{% endif %};">
            {% for status, count in counts.items() %}
            <div class="col-6 col-sm-4 col-md-3 col-lg"> <!-- Responsive columns -->
              <div class="status-card border-{{ status_colors[status] }}">
                <i class="fas {{ status_icons[status] }} status-icon text-{{ status_colors[status] }}"></i>
                <div>
                  <div class="status-text">{{ status }}</div>
                  <div class="status-count text-{{ status_colors[status] }}">{{ count }}</div>
                </div>
              </div>
            </div>
            {% endfor %}
          </div>
        {% endfor %}
      </div>
    </div>

    <!-- Tabs -->
    <ul class="nav nav-pills mb-3" id="reportTabs" role="tablist">
      <li class="nav-item" role="presentation">
        <button class="nav-link" id="wind-tab" data-bs-toggle="pill" data-bs-target="#Wind" type="button" role="tab" aria-controls="Wind" aria-selected="false" onclick="openTab(event, 'Wind')">
          <i class="fas fa-wind me-1"></i>Wind
        </button>
      </li>
      <li class="nav-item" role="presentation">
        <button class="nav-link" id="solar-tab" data-bs-toggle="pill" data-bs-target="#Solar" type="button" role="tab" aria-controls="Solar" aria-selected="false" onclick="openTab(event, 'Solar')">
          <i class="fas fa-sun me-1"></i>Solar
        </button>
      </li>
      <li class="nav-item" role="presentation">
        <button class="nav-link active" id="both-tab" data-bs-toggle="pill" data-bs-target="#Both" type="button" role="tab" aria-controls="Both" aria-selected="true" onclick="openTab(event, 'Both')">
          <i class="fas fa-plug me-1"></i>Both
        </button>
      </li>
      <li class="nav-item ms-auto">
        <a href="http://3.108.243.75:3000/public/dashboard/b092925c-0197-4ea5-b22a-d4ee2118a71e?date=&plant_name=&tab=8-solar" target="_blank" class="nav-link bg-info text-white">
            <i class="fas fa-chart-line me-1"></i>Data Audit Dashboard
        </a>
      </li>
    </ul>

    <!-- Tab Content -->
    <div class="tab-content" id="reportTabsContent">
      <!-- Wind Tab -->
      <div class="tab-pane fade" id="Wind" role="tabpanel" aria-labelledby="wind-tab">
        <div class="card">
          <div class="card-header">Wind Reports</div>
          <div class="card-body p-0"> <!-- Remove padding for full-width table -->
            <div class="table-responsive">
              <table class="table table-hover align-middle">
<thead>
                  <tr>
                    <th>Date</th><th>Plant</th><th>Generation</th><th>Wind Speed</th><th>Status</th><th>Actions</th>
                  </tr>
                </thead>
                <tbody id="windReportTableBody"></tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <!-- Solar Tab -->
      <div class="tab-pane fade" id="Solar" role="tabpanel" aria-labelledby="solar-tab">
        <div class="card">
          <div class="card-header">Solar Reports</div>
           <div class="card-body p-0">
            <div class="table-responsive">
              <table class="table table-hover align-middle">
                <thead>
                  <tr>
                    <th>Date</th><th>Plant</th><th>Generation</th><th>PR</th><th>POA</th><th>Status</th><th>Actions</th>
                  </tr>
                </thead>
                <tbody id="solarReportTableBody"></tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <!-- Both Tab -->
      <div class="tab-pane fade show active" id="Both" role="tabpanel" aria-labelledby="both-tab">
        <div class="card">
          <div class="card-header">Combined Wind & Solar Reports</div>
           <div class="card-body p-0">
            <div class="table-responsive">
              <table class="table table-hover align-middle">
                <thead>
                  <tr>
                    <th>Date</th><th>Solar Plant</th><th>Gen Solar</th><th>PR</th><th>POA</th>
                    <th>Wind Plant</th><th>Gen Wind</th><th>Wind Speed</th><th>Status</th><th>Actions</th>
                  </tr>
                </thead>
                <tbody id="bothReportTableBody"></tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Scripts -->
  <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>
  <!-- No DateRangePicker needed for single date -->

  <script>
    // Function to switch tabs and update status counts visibility
    function openTab(evt, tabName) {
      // Hide all status count containers
      document.getElementById("statusCountsWind").style.display = "none";
      document.getElementById("statusCountsSolar").style.display = "none";
      document.getElementById("statusCountsBoth").style.display = "none";

      // Show the correct status count container
      document.getElementById(`statusCounts${tabName}`).style.display = "flex"; // Use flex for row layout

      // Bootstrap handles tab content visibility via data-bs-target, no manual JS needed for that part
      // However, we need to ensure the correct tab content pane is marked as 'active' and 'show'
      // This might be handled by Bootstrap automatically when clicking the button,
      // but let's ensure our JS doesn't interfere if we were manually managing it.
    }

    // Function to get status badge class
    function getStatusBadge(status) {
        const statusMap = {
          'Sent': { class: 'success', icon: 'fa-check-circle' },
          'Pending': { class: 'secondary', icon: 'fa-clock' },
          'In Review': { class: 'warning', icon: 'fa-search' },
          'Regenerated': { class: 'info', icon: 'fa-sync-alt' },
          'Not Sent': { class: 'danger', icon: 'fa-ban' },
          'Regenerating...': { class: 'primary', icon: 'fa-spinner fa-spin' } // Added for visual feedback
        };
        const config = statusMap[status] || { class: 'light', icon: 'fa-question-circle' };
        return `<span class="badge bg-${config.class}"><i class="fas ${config.icon} me-1"></i>${status}</span>`;
    }


    $(document).ready(function () {
      // Logout functionality
      $('#logoutBtn').click(function () {
        window.location.href = '/logout';
      });

      // Set default start and end date to yesterday
      const yesterday = moment().subtract(1, 'days').format('YYYY-MM-DD');
      $('#startDateInput').val(yesterday);
      $('#endDateInput').val(yesterday);

      // Filter button click handler
      $('#filterBtn').click(function () {
        const $filterBtn = $(this);
        const startDate = $('#startDateInput').val();
        const endDate = $('#endDateInput').val();
        if (!startDate || !endDate) {
            alert('Please select both start and end dates.');
            return;
        }
        if (endDate < startDate) {
            alert('End Date cannot be before Start Date.');
            return;
        }

        // Loading state
        const originalButtonHtml = $filterBtn.html();
        $filterBtn.prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Loading...');

        const endpoints = ['wind', 'solar', 'both'];
        let fetchPromises = [];

        endpoints.forEach(type => {
          const tbody = $(`#${type}ReportTableBody`);
          // More professional loading state
          const colspan = (type === 'wind') ? 7 : (type === 'solar' ? 8 : 11);
          tbody.empty().html(`<tr><td colspan="${colspan}" class="text-center text-muted py-4"><div class="spinner-border spinner-border-sm me-2" role="status"></div>Loading data...</td></tr>`);

          const promise = $.ajax({
            url: `/api/${type}`,
            method: 'GET',
            data: { start_date: startDate, end_date: endDate }
          });

          promise.done(function(data) {
              tbody.empty(); // Clear loading message

              if (!data || data.length === 0) {
                  tbody.html(`<tr><td colspan="${colspan}" class="text-center text-muted py-4">No data found for this date.</td></tr>`);
                  return;
              }

              data.forEach(report => {
                // Define disabling conditions based on prev_index.html logic
                const isApproveDisabled = report.action_performed || report.status === 'Not Sent';
                const isReviewDisabled = report.action_performed && report.status !== 'Not Sent';
                const isRegenDisabled = report.regenerate || (report.action_performed && report.status !== 'Not Sent');
                const isDontSendDisabled = report.status === 'Not Sent' || report.action_performed || report.status === 'Sent';
                const showRegen = report.status === 'In Review'; // Keep this visibility logic
                const showEdit = report.status === 'In Review'; // Show edit button when status is "In Review"

                // Generate buttons using the specific conditions
                const approveBtn = `<button class="btn btn-success btn-sm approve-btn" data-id="${report.id}" data-type="${type}" ${isApproveDisabled ? 'disabled' : ''}><i class="fas fa-check"></i>Approve</button>`;
                const reviewBtn = `<button class="btn btn-warning btn-sm review-btn" data-id="${report.id}" data-type="${type}" ${isReviewDisabled ? 'disabled' : ''}><i class="fas fa-search"></i>Review</button>`;
                const editBtn = `<button class="btn btn-secondary btn-sm edit-btn ${showEdit ? '' : 'd-none'}" data-id="${report.id}" data-type="${type}"><i class="fas fa-edit"></i>Edit</button>`;
                const regenBtn = `<button class="btn btn-info btn-sm regenerate-btn ${showRegen ? '' : 'd-none'}" data-id="${report.id}" data-type="${type}" ${isRegenDisabled ? 'disabled' : ''}><i class="fas fa-sync-alt"></i>Regen</button>`;
                const dontSendBtn = `<button class="btn btn-danger btn-sm dont-send-btn" data-id="${report.id}" data-type="${type}" ${isDontSendDisabled ? 'disabled' : ''}><i class="fas fa-ban"></i>Don't Send</button>`;

                let rowHtml;
                const actionsHtml = `
                    <div class="d-flex flex-nowrap gap-1">
                        ${approveBtn}
                        <div class="btn-action-group"> ${reviewBtn} ${editBtn} ${regenBtn}</div>
                        ${dontSendBtn}
                    </div>`;

                if (type === 'wind') {
                  rowHtml = `<tr data-row-id="${report.id}" data-row-type="${type}">
                      <td>${report.date}</td>
                      <td>${report.plant_long_name} <span class="text-muted">(${report.plant_short_name})</span></td>
                      <td>${report.generation}</td>
                      <td>${report.wind_speed}</td>
                      <td class="status-cell">${getStatusBadge(report.status)}</td>
                      <td>${actionsHtml}</td>
                  </tr>`;
                } else if (type === 'solar') {
                  rowHtml = `<tr data-row-id="${report.id}" data-row-type="${type}">
                      <td>${report.date}</td>
                      <td>${report.plant_long_name} <span class="text-muted">(${report.plant_short_name})</span></td>
                      <td>${report.generation}</td>
                      <td>${report.pr}</td>
                      <td>${report.poa}</td>
                      <td class="status-cell">${getStatusBadge(report.status)}</td>
                      <td>${actionsHtml}</td>
                  </tr>`;
                } else { // Both
                  rowHtml = `<tr data-row-id="${report.id}" data-row-type="${type}">
                      <td>${report.date}</td>
                      <td>${report.plant_long_name_solar} <span class="text-muted">(${report.plant_short_name_solar})</span></td>
                      <td>${report.generation_solar}</td>
                      <td>${report.pr}</td>
                      <td>${report.poa}</td>
                      <td>${report.plant_long_name_wind} <span class="text-muted">(${report.plant_short_name_wind})</span></td>
                      <td>${report.generation_wind}</td>
                      <td>${report.wind_speed}</td>
                      <td class="status-cell">${getStatusBadge(report.status)}</td>
                      <td>${actionsHtml}</td>
                  </tr>`;
                }
                tbody.append(rowHtml);
              });
          });

          promise.fail(function(jqXHR, textStatus, errorThrown) {
              console.error(`Error fetching ${type} data:`, textStatus, errorThrown);
              tbody.empty().html(`<tr><td colspan="${colspan}" class="text-center text-danger py-4">Error loading ${type} data. Please try again.</td></tr>`);
          });

          fetchPromises.push(promise);
        });

        // Fetch and update status counts (top boxes)
        $.ajax({
          url: '/api/status_counts',
          method: 'GET',
          data: { start_date: startDate, end_date: endDate },
          success: function (statusCounts) {
            // For each type (wind, solar, both)
            ['Wind', 'Solar', 'Both'].forEach(function(type) {
              // statusCounts keys are lowercase: wind, solar, both
              var typeKey = type.toLowerCase();
              var container = document.getElementById('statusCounts' + type);
              if (!container) return;
              // For each status card in the container
              $(container).find('.status-card').each(function () {
                var $card = $(this);
                var statusText = $card.find('.status-text').text().trim();
                // statusCounts[typeKey][statusText] gives the count
                var count = (statusCounts[typeKey] && statusCounts[typeKey][statusText]) || 0;
                $card.find('.status-count').text(count);
              });
            });
          }
        });

        // Restore button after all AJAX calls complete
        $.when.apply($, fetchPromises).always(function() {
            $filterBtn.prop('disabled', false).html(originalButtonHtml);
        });
      });

      // --- Action Button Handlers ---

      // Approve or Review Click
      $(document).on('click', '.approve-btn, .review-btn', function () {
        const $button = $(this);
        const id = $button.data('id');
        const type = $button.data('type');
        const isApprove = $button.hasClass('approve-btn');
        const isReview = $button.hasClass('review-btn');
        const updateUrl = `/api/${type}/update`;
        const pdfUrl = `/api/pdf/${type}/${id}`;
        const $row = $button.closest('tr');

        // Optimistic UI update (disable buttons immediately)
        if (isApprove) {
            $row.find('.approve-btn, .review-btn, .regenerate-btn, .dont-send-btn').prop('disabled', true);
        }
        if (isReview) {
            $row.find('.regenerate-btn').removeClass('d-none'); // Show regenerate on review
            $row.find('.edit-btn').removeClass('d-none'); // Show edit button on review
        }

        $.ajax({
          url: updateUrl,
          method: 'POST',
          contentType: 'application/json',
          data: JSON.stringify({ ids: [id], approved: isApprove, review: isReview }),
          success: function (res) {
            // Update status cell
            $row.find('.status-cell').html(getStatusBadge(res.status));

            // Refresh status counts (top boxes) after status change
            updateStatusCounts();

            // Finalize button states based on action
            if (isApprove) {
                $row.find('.regenerate-btn').prop('disabled', true); // Disable regen on approve
                $row.find('.dont-send-btn').prop('disabled', true); // Disable Don't Send on approve
            } else if (isReview) {
                window.open(pdfUrl, '_blank'); // Open PDF only on successful review marking
                // Keep review button enabled
                $row.find('.review-btn').prop('disabled', false);
            }
            // No alert needed, visual feedback is sufficient
          },
          error: function(jqXHR, textStatus, errorThrown) {
             console.error("Error updating report:", textStatus, errorThrown);
             alert("Error updating report status. Please try again.");
             // Revert optimistic UI changes on error? (More complex)
             // For now, just log and alert. Refresh might be needed.
             $('#filterBtn').trigger('click'); // Refresh data on error
          }
        });
      });

      // Regenerate Click
      $(document).on('click', '.regenerate-btn', function () {
        const $button = $(this);
        const id = $button.data('id');
        const type = $button.data('type');
        const $row = $button.closest('tr');

        // Immediate visual feedback
        $row.find('.status-cell').html(getStatusBadge('Regenerating...'));
        $row.find('.approve-btn, .review-btn, .regenerate-btn, .dont-send-btn').prop('disabled', true);

        // Show alert immediately
        let regenTimeMsg = 'Regeneration Started! It might take a few minutes.';
        if (type === 'both') {
          regenTimeMsg = 'Regeneration triggered! It may take up to 4 minutes.';
        } else if (type === 'wind' || type === 'solar') {
          regenTimeMsg = 'Regeneration Started! It may take up to 2 minutes.';
        }
        // Consider using a less intrusive notification method later (e.g., toast)
        // alert(regenTimeMsg);

        $.ajax({
          url: '/api/regenerate',
          method: 'POST',
          contentType: 'application/json',
          data: JSON.stringify({ id: id, type: type }),
          success: function (res) {
            console.log('Regenerate response:', res);
            // Update status based on response (might still be regenerating)
            if (res.row && res.row.status) {
              $row.find('.status-cell').html(getStatusBadge(res.row.status));
            } else {
               $row.find('.status-cell').html(getStatusBadge('Regenerated')); // Fallback
            }
            // Buttons remain disabled after regeneration trigger

            // Refresh data after a delay to allow regeneration
            setTimeout(() => {
                $('#filterBtn').trigger('click');
            }, 5000); // Refresh after 5 seconds (adjust as needed)

            // Polling (optional, can be resource-intensive)
            /*
            let pollCount = 0;
            const maxPolls = 12; // Poll for 2 minutes (12 * 10s)
            const pollInterval = 10000;
            function pollForRegeneratedRow() {
              pollCount++;
              // Fetch only the specific row or refresh all? Refresh all is simpler.
              $('#filterBtn').trigger('click');
              // Check if the status is no longer 'Regenerating...'?
              if ($(`tr[data-row-id=${id}] .status-cell .badge:contains('Regenerating...')`).length > 0 && pollCount < maxPolls) {
                 setTimeout(pollForRegeneratedRow, pollInterval);
              }
            }
            setTimeout(pollForRegeneratedRow, pollInterval);
            */
          },
          error: function (jqXHR, textStatus, errorThrown) {
            alert('Failed to trigger regenerate. Please try again.');
            console.error('Regenerate error:', textStatus, errorThrown);
            // Revert status and enable buttons?
             $('#filterBtn').trigger('click'); // Refresh data on error
          }
        });
      });

      // --- Professional "Don't Send" Modal ---
      // Modal HTML
      if (!document.getElementById('dontSendModal')) {
        $('body').append(`
          <div class="modal fade" id="dontSendModal" tabindex="-1" aria-labelledby="dontSendModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
              <div class="modal-content">
                <div class="modal-header">
                  <h5 class="modal-title" id="dontSendModalLabel"><i class="fas fa-ban text-danger me-2"></i>Mark as "Not Sent"</h5>
                  <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                  <label for="dontSendComment" class="form-label fw-semibold">Please provide a reason for marking this report as <span class="text-danger">"Not Sent"</span>:</label>
                  <textarea id="dontSendComment" class="form-control" rows="3" maxlength="300" placeholder="Enter your comments here..." style="resize: vertical;"></textarea>
                  <div class="form-text text-end"><span id="commentCharCount">0</span>/300 characters</div>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                  <button type="button" class="btn btn-danger" id="submitDontSendBtn">Submit</button>
                </div>
              </div>
            </div>
          </div>
        `);
      }

      let dontSendContext = {};
      $(document).on('click', '.dont-send-btn', function () {
        const $button = $(this);
        const id = $button.data('id');
        const type = $button.data('type');
        const $row = $button.closest('tr');
        dontSendContext = { $button, id, type, $row };

        $('#dontSendComment').val('');
        $('#commentCharCount').text('0');
        const modal = new bootstrap.Modal(document.getElementById('dontSendModal'));
        modal.show();
      });

      // Character count for textarea
      $(document).on('input', '#dontSendComment', function () {
        $('#commentCharCount').text($(this).val().length);
      });

      // Handle submit in modal
      $(document).on('click', '#submitDontSendBtn', function () {
        const comments = $('#dontSendComment').val().trim();
        if (!comments) {
          $('#dontSendComment').addClass('is-invalid');
          $('#dontSendComment').focus();
          return;
        }
        $('#dontSendComment').removeClass('is-invalid');
        const { $button, id, type, $row } = dontSendContext;
        const updateUrl = `/api/${type}/update`;

        // Optimistic UI
        $button.prop('disabled', true);
        $row.find('.approve-btn').prop('disabled', true);
        $row.find('.status-cell').html(getStatusBadge('Not Sent'));

        // Hide modal
        bootstrap.Modal.getInstance(document.getElementById('dontSendModal')).hide();

        $.ajax({
          url: updateUrl,
          method: 'POST',
          contentType: 'application/json',
          data: JSON.stringify({ ids: [id], dont_send: true, comments: comments, status: "Not Sent" }),
          success: function (res) {
            // Status already updated optimistically
            console.log("Marked as 'Not Sent'");
            // Refresh status counts (top boxes) after status change
            updateStatusCounts();
          },
          error: function (jqXHR, textStatus, errorThrown) {
            alert("Failed to mark as 'Not Sent'. Please try again.");
            console.error("Don't Send error:", textStatus, errorThrown);
            $('#filterBtn').trigger('click');
          }
        });
      });

      // Edit Button Click Handler
      $(document).on('click', '.edit-btn', function () {
        const $button = $(this);
        const id = $button.data('id');
        const type = $button.data('type');
        
        // Open edit page in new tab/window
        const editUrl = `/edit/${type}/${id}`;
        window.open(editUrl, '_blank');
      });

      // Helper function to update status counts (top boxes)
      function updateStatusCounts() {
        const startDate = $('#startDateInput').val();
        const endDate = $('#endDateInput').val();
        $.ajax({
          url: '/api/status_counts',
          method: 'GET',
          data: { start_date: startDate, end_date: endDate },
          success: function (statusCounts) {
            ['Wind', 'Solar', 'Both'].forEach(function(type) {
              var typeKey = type.toLowerCase();
              var container = document.getElementById('statusCounts' + type);
              if (!container) return;
              $(container).find('.status-card').each(function () {
                var $card = $(this);
                var statusText = $card.find('.status-text').text().trim();
                var count = (statusCounts[typeKey] && statusCounts[typeKey][statusText]) || 0;
                $card.find('.status-count').text(count);
              });
            });
          }
        });
      }

      // Initial data load
      $('#filterBtn').trigger('click');

      // Set initial active tab's status counts visibility
      // Since 'Both' is active by default, trigger its display logic
      openTab(null, 'Both');

      // Ensure the correct tab content is shown on load (Bootstrap might handle this, but being explicit)
      $('#both-tab').tab('show');


    });
  </script>
</body>
</html>
