# Ignore Python compiled files
__pycache__/
*.pyc
*.pyo
*.pyd

# Ignore environment settings

# Ignore logs
logs


# Ignore cache files
*.db
*.sqlite3

# Ignore IDE/Editor settings
.vscode/
.idea/
*.swp

# Ignore OS-generated files
.DS_Store
Thumbs.db

# Ignore test and coverage files
.coverage
pytest_cache/



# Ignore Docker files (if any)
*.dockerfile
docker-compose.override.yml

# Ignore build artifacts
dist/
build/
*.egg-info

# Ignore report f

# Ignore Ngrok config
ngrok.yml

# Ignore data dumps or temporary files
*.bak
*.tmp
*.backup


project_structure.py

venv
venv/
venv/*


# Ignore all files inside these folders
static/both_final_report/*
static/solar_final_report/*
static/wind_final_report/*
static/plots_solar/*


