<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Edit {{ report_type.title() }} Report</title>
  <link rel="icon" href="{{ url_for('static', filename='logo_integrum.jpg') }}" type="image/jpeg">
  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" />
  <!-- Font Awesome for Icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" />
  <style>
    :root {
      --primary-color: #0d6efd;
      --secondary-color: #6c757d;
      --success-color: #198754;
      --warning-color: #ffc107;
      --danger-color: #dc3545;
      --info-color: #0dcaf0;
      --light-gray: #f8f9fa;
      --medium-gray: #e9ecef;
      --dark-gray: #343a40;
      --body-bg: #f4f7fc;
      --card-bg: #ffffff;
      --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      --border-radius: 0.5rem;
      --font-family-sans-serif: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    body {
      background-color: var(--body-bg);
      font-family: var(--font-family-sans-serif);
      color: var(--dark-gray);
    }

    .navbar {
      background-color: var(--card-bg);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      padding: 0.5rem 1.5rem;
    }

    .navbar-brand img {
      height: 45px;
      width: auto;
    }

    .main-content {
      padding-top: 2rem;
      padding-bottom: 3rem;
    }

    .card {
      background-color: var(--card-bg);
      border: none;
      border-radius: var(--border-radius);
      box-shadow: var(--card-shadow);
      margin-bottom: 1.5rem;
    }

    .card-header {
      background-color: transparent;
      border-bottom: 1px solid var(--medium-gray);
      padding: 1rem 1.5rem;
      font-weight: 600;
      color: var(--primary-color);
    }

    .card-body {
      padding: 1.5rem;
    }

    .form-label {
      font-weight: 500;
      margin-bottom: 0.5rem;
      font-size: 0.9rem;
      color: var(--secondary-color);
    }

    .form-control, .btn {
      border-radius: 0.375rem;
    }

    .btn-primary {
      background-color: var(--primary-color);
      border-color: var(--primary-color);
      transition: background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;
    }
    .btn-primary:hover {
      background-color: #0b5ed7;
      border-color: #0a58ca;
    }

    .btn-secondary {
      background-color: var(--secondary-color);
      border-color: var(--secondary-color);
    }

    .alert {
      border-radius: var(--border-radius);
    }

    .readonly-field {
      background-color: var(--light-gray);
      color: var(--secondary-color);
    }
  </style>
</head>
<body>
  <!-- Navbar -->
  <nav class="navbar navbar-expand-lg navbar-light">
    <div class="container-fluid">
      <a class="navbar-brand" href="/">
        <img src="{{ url_for('static', filename='logo_integrum.jpg') }}" alt="Integrum Logo" />
      </a>
      <div class="d-flex">
        <a href="/" class="btn btn-outline-secondary btn-sm me-2">
          <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
        </a>
        <button id="logoutBtn" class="btn btn-outline-danger btn-sm">
          <i class="fas fa-sign-out-alt me-1"></i>Logout
        </button>
      </div>
    </div>
  </nav>

  <!-- Main Content Area -->
  <div class="container-xl main-content">
    <div class="row justify-content-center">
      <div class="col-lg-8">
        <div class="card">
          <div class="card-header">
            <h4 class="mb-0">
              <i class="fas fa-edit me-2"></i>Edit {{ report_type.title() }} Report
            </h4>
          </div>
          <div class="card-body">
            <!-- Alert for messages -->
            <div id="alertContainer"></div>

            <form id="editForm" method="POST">
              <div class="row">
                <!-- Date Field (Read-only) -->
                <div class="col-md-6 mb-3">
                  <label for="date" class="form-label">Date</label>
                  <input type="text" class="form-control readonly-field" id="date" name="date" 
                         value="{{ report.date }}" readonly>
                </div>

                <!-- Status Field (Read-only) -->
                <div class="col-md-6 mb-3">
                  <label for="status" class="form-label">Status</label>
                  <input type="text" class="form-control readonly-field" id="status" name="status" 
                         value="{{ report.status }}" readonly>
                </div>
              </div>

              {% if report_type == 'wind' %}
                <!-- Wind Report Fields -->
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label for="plant_short_name" class="form-label">Plant Short Name</label>
                    <input type="text" class="form-control readonly-field" id="plant_short_name" 
                           name="plant_short_name" value="{{ report.plant_short_name }}" readonly>
                  </div>
                  <div class="col-md-6 mb-3">
                    <label for="plant_long_name" class="form-label">Plant Long Name</label>
                    <input type="text" class="form-control readonly-field" id="plant_long_name" 
                           name="plant_long_name" value="{{ report.plant_long_name }}" readonly>
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label for="generation" class="form-label">Generation (MWh)</label>
                    <input type="number" step="0.01" class="form-control" id="generation" 
                           name="generation" value="{{ report.generation }}" required>
                  </div>
                  <div class="col-md-6 mb-3">
                    <label for="wind_speed" class="form-label">Wind Speed (m/s)</label>
                    <input type="number" step="0.01" class="form-control" id="wind_speed" 
                           name="wind_speed" value="{{ report.wind_speed }}" required>
                  </div>
                </div>

              {% elif report_type == 'solar' %}
                <!-- Solar Report Fields -->
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label for="plant_short_name" class="form-label">Plant Short Name</label>
                    <input type="text" class="form-control readonly-field" id="plant_short_name" 
                           name="plant_short_name" value="{{ report.plant_short_name }}" readonly>
                  </div>
                  <div class="col-md-6 mb-3">
                    <label for="plant_long_name" class="form-label">Plant Long Name</label>
                    <input type="text" class="form-control readonly-field" id="plant_long_name" 
                           name="plant_long_name" value="{{ report.plant_long_name }}" readonly>
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-4 mb-3">
                    <label for="generation" class="form-label">Generation (MWh)</label>
                    <input type="number" step="0.01" class="form-control" id="generation" 
                           name="generation" value="{{ report.generation }}" required>
                  </div>
                  <div class="col-md-4 mb-3">
                    <label for="pr" class="form-label">PR (%)</label>
                    <input type="number" step="0.01" class="form-control" id="pr" 
                           name="pr" value="{{ report.pr }}" required>
                  </div>
                  <div class="col-md-4 mb-3">
                    <label for="poa" class="form-label">POA (kWh/m²)</label>
                    <input type="number" step="0.01" class="form-control" id="poa" 
                           name="poa" value="{{ report.poa }}" required>
                  </div>
                </div>

              {% elif report_type == 'both' %}
                <!-- Both Report Fields -->
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label for="plant_short_name_solar" class="form-label">Solar Plant Short Name</label>
                    <input type="text" class="form-control readonly-field" id="plant_short_name_solar" 
                           name="plant_short_name_solar" value="{{ report.plant_short_name_solar }}" readonly>
                  </div>
                  <div class="col-md-6 mb-3">
                    <label for="plant_long_name_solar" class="form-label">Solar Plant Long Name</label>
                    <input type="text" class="form-control readonly-field" id="plant_long_name_solar" 
                           name="plant_long_name_solar" value="{{ report.plant_long_name_solar }}" readonly>
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label for="plant_short_name_wind" class="form-label">Wind Plant Short Name</label>
                    <input type="text" class="form-control readonly-field" id="plant_short_name_wind" 
                           name="plant_short_name_wind" value="{{ report.plant_short_name_wind }}" readonly>
                  </div>
                  <div class="col-md-6 mb-3">
                    <label for="plant_long_name_wind" class="form-label">Wind Plant Long Name</label>
                    <input type="text" class="form-control readonly-field" id="plant_long_name_wind" 
                           name="plant_long_name_wind" value="{{ report.plant_long_name_wind }}" readonly>
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-3 mb-3">
                    <label for="generation_solar" class="form-label">Solar Generation (MWh)</label>
                    <input type="number" step="0.01" class="form-control" id="generation_solar" 
                           name="generation_solar" value="{{ report.generation_solar }}" required>
                  </div>
                  <div class="col-md-3 mb-3">
                    <label for="pr" class="form-label">PR (%)</label>
                    <input type="number" step="0.01" class="form-control" id="pr" 
                           name="pr" value="{{ report.pr }}" required>
                  </div>
                  <div class="col-md-3 mb-3">
                    <label for="poa" class="form-label">POA (kWh/m²)</label>
                    <input type="number" step="0.01" class="form-control" id="poa" 
                           name="poa" value="{{ report.poa }}" required>
                  </div>
                  <div class="col-md-3 mb-3">
                    <label for="generation_wind" class="form-label">Wind Generation (MWh)</label>
                    <input type="number" step="0.01" class="form-control" id="generation_wind" 
                           name="generation_wind" value="{{ report.generation_wind }}" required>
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label for="wind_speed" class="form-label">Wind Speed (m/s)</label>
                    <input type="number" step="0.01" class="form-control" id="wind_speed" 
                           name="wind_speed" value="{{ report.wind_speed }}" required>
                  </div>
                </div>
              {% endif %}

              <!-- Comments Field -->
              <div class="mb-3">
                <label for="comments" class="form-label">Comments</label>
                <textarea class="form-control" id="comments" name="comments" rows="3" 
                          placeholder="Add any comments about the changes...">{{ report.comments or '' }}</textarea>
              </div>

              <!-- Form Actions -->
              <div class="d-flex justify-content-end gap-2">
                <button type="button" class="btn btn-secondary" onclick="window.close()">
                  <i class="fas fa-times me-1"></i>Cancel
                </button>
                <button type="submit" class="btn btn-primary">
                  <i class="fas fa-save me-1"></i>Save Changes
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Scripts -->
  <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
