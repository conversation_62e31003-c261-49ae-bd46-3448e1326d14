import os
from helper.utils import (
    generate_solar_dgr_pdf,
    merge_pdfs, get_dynamic_dates, fetch_data_total,
    get_capacity_from_csv
)
from helper.logger_setup import setup_logger
from helper.storage_s3 import upload_file_s3
# Import enhanced plotting functions with <PERSON>born + Matplotlib hybrid styling
from helper.solar_plots_enhanced import (
    plot_monthly_generation,
    plot_yearly_generation,
    plot_pr_monthly,
    generate_solar_plot
)
from DB.db_ops import insert_solar_data_db, get_solar_report_data

# Set up logger
logging = setup_logger('solar_automation', 'solar_automation.log')


def fetch_all_data(plant_name, start_date, current_month_start, last_30_days_start, current_year_start, yearly, condition_poa, condition_pr, condition_generation, condition_monthly_pr):
    """
    Runs all fetch tasks sequentially to reduce RAM usage.
    """
    logging.info(f"Starting sequential data fetch for plant: {plant_name}")

    # Fetch data sequentially instead of concurrently
    poa_data = fetch_data_total(plant_name, ['Daily POA Energy'], 'Plant', start_date, start_date, condition_poa)
    pr_data = fetch_data_total(plant_name, ['PR'], 'Plant', start_date, start_date, condition_pr)
    daily_generation = fetch_data_total(plant_name, ['Daily Energy'], 'Plant', start_date, start_date, condition_generation)
    generation_monthly_value = fetch_data_total(plant_name, ['Daily Energy'], 'Plant', current_month_start, start_date, condition_generation)
    generation_yearly_value = fetch_data_total(plant_name, ['Daily Energy'], 'Plant', current_year_start, start_date, condition_generation)
    pr_data_monthly_value = fetch_data_total(plant_name, ['PR'], 'Plant', current_month_start, start_date, condition_monthly_pr)
    pr_data_yearly_value = fetch_data_total(plant_name, ['PR'], 'Plant', current_year_start, start_date, condition_monthly_pr)
    poa_data_monthly_value = fetch_data_total(plant_name, ['Daily POA Energy'], 'Plant', current_month_start, start_date, condition_poa)
    poa_data_yearly_value = fetch_data_total(plant_name, ['Daily POA Energy'], 'Plant', current_year_start, start_date, condition_poa)
    generation_monthly = fetch_data_total(plant_name, ['Daily Energy'], 'Plant', last_30_days_start, start_date, condition_generation)
    # generation_yearly = fetch_data_total(plant_name, ['Daily Energy'], 'Plant', yearly, start_date, condition_generation)
    # pr_data_monthly = fetch_data_total(plant_name, ['PR'], 'Plant', last_30_days_start, start_date, condition_monthly_pr)
    # pr_data_yearly = fetch_data_total(plant_name, ['PR'], 'Plant', yearly, start_date, condition_monthly_pr)

    logging.info(f"Completed sequential data fetch for plant: {plant_name}")

    return {
        "poa_data": poa_data,
        "pr_data": pr_data,
        "daily_generation": daily_generation,
        "generation_monthly_value": generation_monthly_value,
        "generation_yearly_value": generation_yearly_value,
        "pr_data_monthly_value": pr_data_monthly_value,
        "pr_data_yearly_value": pr_data_yearly_value,
        "poa_data_monthly_value": poa_data_monthly_value,
        "poa_data_yearly_value": poa_data_yearly_value,
        "generation_monthly": generation_monthly,
        # "generation_yearly": generation_yearly,
        # "pr_data_monthly": pr_data_monthly,
        # "pr_data_yearly": pr_data_yearly,
    }


def generate_solar_automation_report(plant_name, start_date, customer_name, project):
    """
    Sequential solar report generation to reduce RAM usage.
    """
    logging.info(f"Starting report generation for Plant: {plant_name}, Date: {start_date}, Customer: {customer_name}, Project: {project}")

    try:
        # Define conditions for fetching data
        condition_poa = {"Daily POA Energy": "last"}
        condition_daily_pr = {"PR": "last"}
        condition_monthly_pr = {"PR": "mean"}
        condition_generation = {"Daily Energy": "max"}

        # yearly = '2024-04-01'
        (current_month_start, _, _,
         _, _, last_30_days_start, current_year_start, last_year_date) = get_dynamic_dates(start_date)

        # Fetch data sequentially
        data = fetch_all_data(plant_name, start_date, current_month_start, last_30_days_start, current_year_start, last_year_date, condition_poa, condition_daily_pr, condition_generation, condition_monthly_pr)

        # Extract data from the fetched results
        poa_data = data["poa_data"]
        pr_data = data["pr_data"]
        daily_generation = data["daily_generation"]
        generation_monthly_value = data["generation_monthly_value"]
        generation_yearly_value = data["generation_yearly_value"]
        pr_data_monthly_value = data["pr_data_monthly_value"]
        pr_data_yearly_value = data["pr_data_yearly_value"]
        poa_data_monthly_value = data["poa_data_monthly_value"]
        poa_data_yearly_value = data["poa_data_yearly_value"]
        # generation_monthly = data["generation_monthly"]
        # generation_yearly = data["generation_yearly"]
        # pr_data_monthly = data["pr_data_monthly"]
        # pr_data_yearly = data["pr_data_yearly"]

        # Calculate required values (checking for empty DataFrames)
        total_generation = daily_generation.iloc[:, 1:].sum().sum() if not daily_generation.empty else 0
        month_gen_value = generation_monthly_value.iloc[:, 1:].sum().sum() if not generation_monthly_value.empty else 0
        year_gen_value = generation_yearly_value.iloc[:, 1:].sum().sum() if not generation_yearly_value.empty else 0

        month_pr_value = pr_data_monthly_value.iloc[:, 1:].mean().mean() if not pr_data_monthly_value.empty else 0
        daily_pr_percentage = pr_data.iloc[:, 1:].mean().mean() if not pr_data.empty else 0
        year_pr_value = pr_data_yearly_value.iloc[:, 1:].mean().mean() if not pr_data_yearly_value.empty else 0

        monthly_poa_value = poa_data_monthly_value.iloc[:, 1:].mean().mean() if not poa_data_monthly_value.empty else 0
        yearly_poa_value = poa_data_yearly_value.iloc[:, 1:].mean().mean() if not poa_data_yearly_value.empty else 0
        avg_poa = poa_data.iloc[:, 1:].mean().mean() if not poa_data.empty else 0

        # Generate and save plots (assuming these functions are synchronous)
        # plot_month_generation = plot_monthly_generation(generation_monthly, plant_name)
        # plot_year_generation = plot_yearly_generation(generation_yearly, plant_name)
        # plot_month_pr = plot_pr_monthly(pr_data_monthly, plant_name)
        # plot_year_pr = plot_pr_monthly(pr_data_yearly, plant_name)

        # image_files = [plot_month_generation, plot_year_generation, plot_month_pr, plot_year_pr]

        # custom_titles = [
        #     "Daily Generation Over Last 30 Days",
        #     "Last 12 Months Energy Generation",
        #     "AVG PR% Over Last 30 Days",
        #     "AVG PR % Over Last 12 Months"
        # ]

        # Map data values for plots and annotations
        # data_values = {
        #     "daily_generation.png": (round(float(total_generation), 2), "KWh"),
        #     "monthly_generation.png": (round(float(month_gen_value), 2), "KWh"),
        #     "yearly_generation.png": (round(float(year_gen_value), 2), "KWh"),
        #     "daily_pr.png": (round(float(daily_pr_percentage), 2), "%"),
        #     "monthly_pr.png": (round(float(month_pr_value), 2), "%"),
        #     "yearly_pr.png": (round(float(year_pr_value), 2), "%")
        # }

        # final_plot_path = generate_solar_plot(
        #     image_files, data_values, plant_name + "new_solar_report.pdf", custom_titles, customer_name, start_date
        # )

        # Generate summary reports
        # capacities = {"IN.INTE.KIDS": "4.8", "IN.INTE.SAAB": "1.9"}
        # capacite = capacities.get(plant_name, "N/A")
        capacity = get_capacity_from_csv(plant_name)

        

        final_pdf_path = os.path.join("static", "solar_final_report", f"{plant_name}_DGR_{start_date}.pdf")
        # final_pdf = merge_pdfs(summary_pdf, final_plot_path, final_pdf_path)


        summary_pdf = generate_solar_dgr_pdf(
            start_date, customer_name, avg_poa, daily_pr_percentage, total_generation,
            month_gen_value, month_pr_value, monthly_poa_value, capacity, final_pdf_path
        )

        

        logging.info(f"Report generated successfully for {plant_name} - {summary_pdf}")

        # Clean up generated files synchronously
        # files_to_delete = [summary_pdf, final_plot_path] + image_files
        # for file in files_to_delete:
        #     if file and os.path.exists(file):
        #         os.remove(file)

        # file_path = "solar_reports/" + f"{plant_name}_DGR_{start_date}.pdf"  # Relative path for S3
        # upload_file_s3(summary_pdf, file_path)


        result = get_solar_report_data(plant_name, start_date)

        if result:
            print("the data is available")
            data = [{
                "date": start_date,
                "plant_short_name": plant_name,
                "plant_long_name": customer_name,
                "generation": round(float(total_generation), 2),
                "pr": round(float(daily_pr_percentage), 2),
                "poa": round(float(avg_poa), 2),
                "approved": 0,
                "review": 0,
                "action_performed": 0,
                "dgr_path": summary_pdf,
                "status": "Regenerated"
            }]
        else:
            data = [{
                "date": start_date,
                "plant_short_name": plant_name,
                "plant_long_name": customer_name,
                "generation": round(float(total_generation), 2),
                "pr": round(float(daily_pr_percentage), 2),
                "poa": round(float(avg_poa), 2),
                "approved": 0,
                "review": 0,
                "action_performed": 0,
                "dgr_path": summary_pdf
            }]
        # Insert data into the database
        insert_solar_data_db(data)
        logging.info(f"Data inserted into database for {plant_name}")


        return summary_pdf

    except Exception as e:
        logging.error(f"Error generating report for {plant_name}: {e}", exc_info=True)
        raise
