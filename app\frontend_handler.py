import os
from threading import Thread

from DB.setup_db import session as db_session

from flask import (
    jsonify, request, render_template,
    send_from_directory, flash, url_for, redirect, session, current_app as app
)

from functools import wraps # Import wraps

from app.process_tasks import (
    send_whatsapp_report_solar,
    send_whatsapp_report_wind,
    send_whatsapp_report_both
)

from DB.models import WindReport, SolarReport, DgrBothDb

from helper.logger_setup import setup_logger

from app.regenerate_reports import regenerate_task_both, regenerate_task_solar, regenerate_task_wind

# Logger Setup
logger = setup_logger('frontend_handler', 'frontend_handler.log')



EMAIL_CREDENTIAL = os.getenv("APP_LOGIN_EMAIL")
PASSWORD_CREDENTIAL = os.getenv("APP_LOGIN_PASSWORD")


# ---------------------------- ROUTES ---------------------------- #


# Login required decorator
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'logged_in' not in session:
            flash('Please log in to access this page.', 'warning')
            return redirect(url_for('login', next=request.url))
        return f(*args, **kwargs)
    return decorated_function



def login():
    if request.method == 'POST':
        email = request.form['email']
        password = request.form['password']
        if email == EMAIL_CREDENTIAL and password == PASSWORD_CREDENTIAL:
            session['logged_in'] = True
            flash('Login successful!', 'success')
            next_url = request.args.get('next')
            return redirect(next_url or url_for('index'))
        else:
            flash('Invalid credentials. Please try again.', 'danger')
    # If GET request or failed POST, show login page
    return render_template('login.html')


def logout():
    session.pop('logged_in', None)
    flash('You have been logged out.', 'info')
    return redirect(url_for('login'))

# @app.route('/')
@login_required # Protect the main page
def index():
    from DB.models import WindReport, SolarReport, DgrBothDb
    from sqlalchemy import func

    db = db_session()
    try:
        statuses = ['Sent', 'Pending', 'In Review', 'Regenerated', 'Not Sent']

        # Count for WindReport
        wind_counts = dict(db.query(WindReport.status, func.count(WindReport.id)).group_by(WindReport.status).all())
        # Count for SolarReport
        solar_counts = dict(db.query(SolarReport.status, func.count(SolarReport.id)).group_by(SolarReport.status).all())
        # Count for DgrBothDb
        both_counts = dict(db.query(DgrBothDb.status, func.count(DgrBothDb.id)).group_by(DgrBothDb.status).all())

        # Prepare per-type status counts
        status_counts = {
            'wind': {status: wind_counts.get(status, 0) for status in statuses},
            'solar': {status: solar_counts.get(status, 0) for status in statuses},
            'both': {status: both_counts.get(status, 0) for status in statuses}
        }

        return render_template('index.html', status_counts=status_counts)
    finally:
        db_session.remove()

@login_required # Protect API route
def get_dgr_reports():
    start_date = request.args.get('start_date', '').strip()
    end_date = request.args.get('end_date', '').strip()

    db = db_session()  # ← Use renamed session
    try:
        query = db.query(WindReport)
        if start_date and end_date:
            query = query.filter(WindReport.date.between(start_date, end_date))
        reports = query.all()
        return jsonify([report.to_dict() for report in reports])
    except Exception as e:
        print("❌ Error in get_dgr_reports:", e)
        return jsonify({"error": "internal server error"}), 500
    finally:
        db_session.remove() # Use scoped_session's remove method

@login_required # Protect API route
def get_solar_reports():
    start_date = request.args.get("start_date")
    end_date = request.args.get("end_date")

    db = db_session()  # ← Use renamed session
    try:
        query = db.query(SolarReport).filter(
            SolarReport.date.between(start_date, end_date)
        )
        reports = query.all()
        return jsonify([r.to_dict() for r in reports])
    except Exception as e:
        print("❌ Error in get_solar_reports:", e)
        return jsonify({"error": "internal server error"}), 500
    finally:
        db_session.remove() # Use scoped_session's remove method


@login_required # Protect API route
def get_both_reports():
    start_date = request.args.get('start_date', '').strip()
    end_date = request.args.get('end_date', '').strip()

    db = db_session()  # ← Use renamed session
    try:
        query = db.query(DgrBothDb)
        if start_date and end_date:
            query = query.filter(DgrBothDb.date.between(start_date, end_date))
        reports = query.all()
        return jsonify([report.to_dict() for report in reports])
    except Exception as e:
        print("❌ Error in get_both_reports:", e)
        return jsonify({"error": "internal server error"}), 500
    finally:
        db_session.remove() # Use scoped_session's remove method

@login_required # Protect API route
def _update_and_send(report, report_type, send_report_fn):
    try:
        if not report:
            return

        report.approved = True
        report.review = False
        report.status = 'Sent'
        report.action_performed = True

        plant = getattr(report, 'plant_short_name', '') or getattr(report, 'plant_short_name_solar', '')
        customer = getattr(report, 'plant_long_name', '') or getattr(report, 'plant_long_name_solar', '')
        report_date = report.date.strftime('%Y-%m-%d')

        def run():
            send_report_fn(plant, customer, report_date)

        Thread(target=run).start()

    except Exception as e:
        logger.error(f"Failed to trigger WhatsApp report for {report_type}: {e}")



@login_required # Protect API route
def update_dgr_wind():
    data = request.json
    ids = data['ids']
    approved = data.get('approved')
    review = data.get('review')
    dont_send = data.get('dont_send')
    comments = data.get('comments')

    updated_reports = []
    db = db_session() # Get session
    try:
        for id in ids:
            report = db.get(WindReport, id) # Use db session
            if report:
                if dont_send:
                    report.status = "Not Sent"
                    report.dont_send = True
                    if comments is not None:
                        report.comments = comments
                else:
                    report.approved = approved
                    report.review = False
                    # Only set action_performed to True if approved
                    if approved:
                        report.status = 'Sent'
                        _update_and_send(report, 'wind', send_whatsapp_report_wind)
                        report.action_performed = True # Mark as final action only on approval
                    # If only review is True, don't set action_performed
                    elif review:
                        report.status = 'In Review'
                        report.action_performed = False # Ensure it stays false if only reviewing
            db.commit() # Use db session
            updated_reports.append(report.to_dict())


        return jsonify(updated_reports[0])  # Returning the updated record for frontend
    except Exception as e:
        db.rollback()
        print(f"❌ Error updating wind report: {e}")
        return jsonify({"error": "Failed to update report"}), 500
    finally:
        db_session.remove() # Close session



@login_required # Protect API route
def update_solar():
    data = request.json
    ids = data['ids']
    approved = data.get('approved')
    review = data.get('review')
    dont_send = data.get('dont_send')
    comments = data.get('comments')

    updated_reports = []
    db = db_session() # Get session
    try:
        for id in ids:
            report = db.get(SolarReport, id) # Use db session
            if report:
                if dont_send:
                    report.status = "Not Sent"
                    report.dont_send = True
                    if comments is not None:
                        report.comments = comments
                else:
                    report.approved = approved
                    report.review = False
                    # Only set action_performed to True if approved
                    if approved:
                        report.status = 'Sent'
                        report.action_performed = True # Mark as final action only on approval
                        _update_and_send(report, 'solar', send_whatsapp_report_solar)
                    # If only review is True, don't set action_performed
                    elif review:
                        report.status = 'In Review'
                        report.action_performed = False # Ensure it stays false if only reviewing
            db.commit() # Use db session
            updated_reports.append(report.to_dict())


        return jsonify(updated_reports[0])
    except Exception as e:
        db.rollback()
        print(f"❌ Error updating solar report: {e}")
        return jsonify({"error": "Failed to update report"}), 500
    finally:
        db_session.remove() # Close session



@login_required # Protect API route
def update_both():
    data = request.json
    ids = data['ids']
    approved = data.get('approved')
    review = data.get('review')
    dont_send = data.get('dont_send')
    comments = data.get('comments')

    updated_reports = []
    db = db_session() # Get session
    try:
        for id in ids:
            report = db.get(DgrBothDb, id) # Use db session
            if report:
                if dont_send:
                    report.status = "Not Sent"
                    report.dont_send = True
                    if comments is not None:
                        report.comments = comments
                else:
                    report.approved = approved
                    report.review = False
                    # Only set action_performed to True if approved
                    if approved:
                        report.status = 'Sent'
                        report.action_performed = True # Mark as final action only on approval

                        def run():
                                send_whatsapp_report_both(
                                    report.plant_short_name_solar,
                                    report.plant_short_name_wind,
                                    report.plant_long_name_solar,
                                    report.date.strftime('%Y-%m-%d')
                                )

                        Thread(target=run).start()


                    # If only review is True, don't set action_performed
                    elif review:
                        report.status = 'In Review'
                        report.action_performed = False # Ensure it stays false if only reviewing
            db.commit() # Use db session
            updated_reports.append(report.to_dict())
            print("Updated report:", report.to_dict())


        return jsonify(updated_reports[0]) # Returning the updated record for frontend
    except Exception as e:
        db.rollback()
        print(f"❌ Error updating both report: {e}")
        return jsonify({"error": "Failed to update report"}), 500
    finally:
        db_session.remove() # Close session



@login_required # Protect API route
def get_status_counts():
    from sqlalchemy import func

    start_date = request.args.get('start_date', '').strip()
    end_date = request.args.get('end_date', '').strip()
    statuses = ['Sent', 'Pending', 'In Review', 'Regenerated', 'Not Sent']

    db = db_session()
    try:
        # Wind
        wind_query = db.query(WindReport.status, func.count(WindReport.id))
        if start_date and end_date:
            wind_query = wind_query.filter(WindReport.date.between(start_date, end_date))
        wind_counts = dict(wind_query.group_by(WindReport.status).all())

        # Solar
        solar_query = db.query(SolarReport.status, func.count(SolarReport.id))
        if start_date and end_date:
            solar_query = solar_query.filter(SolarReport.date.between(start_date, end_date))
        solar_counts = dict(solar_query.group_by(SolarReport.status).all())

        # Both
        both_query = db.query(DgrBothDb.status, func.count(DgrBothDb.id))
        if start_date and end_date:
            both_query = both_query.filter(DgrBothDb.date.between(start_date, end_date))
        both_counts = dict(both_query.group_by(DgrBothDb.status).all())

        status_counts = {
            'wind': {status: wind_counts.get(status, 0) for status in statuses},
            'solar': {status: solar_counts.get(status, 0) for status in statuses},
            'both': {status: both_counts.get(status, 0) for status in statuses}
        }
        return jsonify(status_counts)
    finally:
        db_session.remove()

@login_required # Protect API route
def serve_pdf(report_type, report_id):
    logger.info(f"Serving PDF for type: {report_type}, ID: {report_id}")
    db = db_session()

    try:
        model_map = {
            'wind': WindReport,
            'solar': SolarReport,
            'both': DgrBothDb
        }

        model = model_map.get(report_type)
        if not model:
            return jsonify({"error": "Invalid report type"}), 400

        report = db.get(model, report_id)
        if not report:
            return jsonify({"error": "Report not found"}), 404

        relative_pdf_path = report.dgr_path
        if not relative_pdf_path:
            return jsonify({"error": "PDF path not available"}), 404

        normalized_path = os.path.normpath(relative_pdf_path)
        pdf_dir = os.path.dirname(normalized_path)
        pdf_file = os.path.basename(normalized_path)

        # Update the root directory as needed
        root = "D:\\Harikrishnan\\DGR-Generation-New\\" # Local path
        #root = "//home/<USER>//dgr_updated//DGR-Generation//" #Deployment path



        abs_path = os.path.join(root, pdf_dir, pdf_file)
        # abs_path = os.path.join(app.root_path, pdf_dir, pdf_file)

        if not os.path.exists(abs_path):
            logger.warning(f"PDF not found: {abs_path}")
            return jsonify({"error": "PDF file not found"}), 404

        return send_from_directory(os.path.dirname(abs_path), pdf_file, as_attachment=False)

    except Exception as e:
        logger.exception("Failed to serve PDF")
        return jsonify({"error": "Internal server error"}), 500

    finally:
        db.close()





def fetch_report_by_type_and_id(report_type, report_id):
    """
    Fetch a report by type and ID from the database.

    Args:
        report_type (str): One of "wind", "solar", or "both".
        report_id (int): The primary key ID of the report.

    Returns:
        dict: The report as a dictionary, or an error message.
    """
    db = db_session()
    try:
        model_map = {
            "wind": WindReport,
            "solar": SolarReport,
            "both": DgrBothDb
        }

        model = model_map.get(report_type)
        if not model:
            raise ValueError(f"Invalid report type: {report_type}")

        report = db.get(model, report_id)
        print("Fetched report:", report.to_dict() if report else None)
        if report:
            return report.to_dict()
        else:
            return {"error": "Report not found"}
    finally:
        db.close()





@login_required
def api_regenerate():
    data = request.json
    report_id = data.get('id')
    report_type = data.get('type')
    plant_long_name_solar = None
    date = None


    # Fetch report data as dict for regeneration task input
    report_data = fetch_report_by_type_and_id(report_type, report_id)

    db = db_session()
    try:
        report = None

        if report_type == 'wind':
            report = db.get(WindReport, report_id)
            if report:
                plant_long_name_solar = report.plant_long_name
                date = str(report.date)

                # Use dict for regeneration task input
                wind_data = {
                    "date": date,
                    "plant_short_name": [report_data['plant_short_name']],
                    "plant_long_name": [report_data['plant_long_name']],
                    "original_id": report_id
                }
                print("Wind data for regeneration:", wind_data)
                # Mark the report as being regenerated BEFORE running the task
                report.status = 'Regenerated'
                report.regenerate = True
                report.action_performed = True
                db.commit()
                regenerate_task_wind(wind_data)

        elif report_type == 'solar':
            report = db.get(SolarReport, report_id)
            if report:
                plant_long_name_solar = report.plant_long_name
                date = str(report.date)

                solar_data = {
                    "date": date,
                    "plant_short_name": [report_data['plant_short_name']],
                    "plant_long_name": [report_data['plant_long_name']],
                    "original_id": report_id
                }
                report.status = 'Regenerated'
                report.regenerate = True
                report.action_performed = True
                db.commit()
                regenerate_task_solar(solar_data)

        elif report_type == 'both':
            report = db.get(DgrBothDb, report_id)
            if report:
                plant_long_name_solar = report.plant_long_name_solar
                date = report.date.strftime('%Y-%m-%d')

                both_data = {
                    "date": date,
                    "plant_short_name_wind": [report_data['plant_short_name_wind']],
                    "plant_short_name_solar": [report_data['plant_short_name_solar']],
                    "plant_long_name_wind": [report_data['plant_long_name_wind']],
                    "original_id": report_id
                }
                report.status = 'Regenerated'
                report.regenerate = True
                report.action_performed = True
                db.commit()
                regenerate_task_both(both_data)

        # Re-fetch the report to ensure it is bound to the session
        if report:
            if report_type == 'wind':
                report = db.get(WindReport, report_id)
            elif report_type == 'solar':
                report = db.get(SolarReport, report_id)
            elif report_type == 'both':
                report = db.get(DgrBothDb, report_id)

            print("Regenerated row data:", report.to_dict() if report else None)

        return jsonify({
            "success": True,
            "message": "Regenerate triggered",
            "id": report_id,
            "type": report_type,
            "plant_long_name_solar": plant_long_name_solar,
            "date": date,
            "row": report.to_dict() if report else None
        })

    except Exception as e:
        print("Error in /api/regenerate:", e)
        return jsonify({
            "success": False,
            "message": "Error in regenerate",
            "id": report_id,
            "type": report_type
        }), 500

    finally:
        db_session.remove()
