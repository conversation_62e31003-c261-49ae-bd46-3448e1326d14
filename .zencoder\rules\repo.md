---
description: Repository Information Overview
alwaysApply: true
---

# Wind & Solar Automation Platform Information

## Summary
A modular, production-ready platform for automating daily generation reports (DGR) for wind and solar plants, integrating with WhatsApp for report delivery, and supporting advanced data processing, plotting, and PDF generation.

## Structure
The project follows a modular structure with clear separation of concerns:
- **app/**: Web routes, frontend handlers, scheduling, and webhook endpoints
- **DB/**: Database models and operations using SQLAlchemy
- **helper/**: Utilities for plotting, PDF generation, logging, S3 storage
- **src/**: Core automation logic for wind/solar/both plants
- **whatsapp/**: WhatsApp integration for message sending and extraction
- **config/**: Configuration and settings management
- **static/**: Static files (customer data, logos, reports, plots)
- **templates/**: HTML templates for web interface

## Language & Runtime
**Language**: Python
**Version**: Python 3.12 (based on .pyc files)
**Framework**: Flask 3.1.0
**Database**: MySQL with SQLAlchemy ORM
**Package Manager**: pip

## Dependencies
**Main Dependencies**:
- Flask==3.1.0 - Web framework
- reportlab==4.3.1 - PDF generation
- PyPDF2==3.0.1 - PDF manipulation
- pandas==2.2.3 - Data processing
- matplotlib==3.10.0 - Data visualization
- schedule==1.2.2 - Task scheduling
- boto3 - AWS S3 integration
- sqlalchemy - ORM for database operations
- pymysql - MySQL connector

**Development Dependencies**:
- python-dotenv==1.0.1 - Environment variable management

## Build & Installation
```bash
# Create and activate virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Set up environment variables
# Copy .env.example to .env and fill in required values

# Start the application
python main.py
```

## Database
**Type**: MySQL
**Connection**: Configured via DB_URL environment variable
**Models**:
- WindReport - Stores wind plant report data
- SolarReport - Stores solar plant report data
- DgrBothDb - Stores combined wind/solar plant report data
- WhatsAppMessage - Tracks sent WhatsApp messages

## External Services
**WhatsApp Business API**: Used for sending reports to customers
**AWS S3**: Optional storage for reports and data (configured via environment variables)

## Main Entry Points
**Application**: main.py - Creates Flask app and starts scheduler
**Web Routes**: app/routes.py - Defines HTTP endpoints
**Automation**: 
- src/wind_automation.py - Wind plant report generation
- src/solar_automation.py - Solar plant report generation
- src/both_plants_automation.py - Combined plant report generation

## Configuration
**Environment Variables**:
- VERIFY_TOKEN - Token for webhook verification
- PUBLIC_URL - Publicly accessible URL for webhooks
- WABATOKEN - WhatsApp Business API token
- DB_URL - Database connection string
- AWS_* - AWS S3 configuration (when USE_S3=True)

**Settings File**: config/settings.py - Centralizes configuration loading