
import os
from helper.utils import (
    fetch_data_total,
    generate_dgr_wind_report,
    get_dynamic_dates,
    generate_combined_wind_pdf,
    get_capacity_from_csv
)
from helper.logger_setup import setup_logger
# Import enhanced plotting functions with <PERSON>born + Matplotlib hybrid styling
from helper.wind_plots_enhanced import (
    plot_plant_wise_generation_pdf,
    plot_wind_speed_last_12_months,
    plot_wind_speed_last_30_days,
    plot_daily_generation_last_30_days,
    generate_wind_plot
)
# Import the PDF merging function from the original wind_plots module
from helper.wind_plots import merge_pdfs_plots
from helper.storage_s3 import upload_file_s3
from DB.db_ops import insert_wind_data_db, get_wind_report_data
# Set up logging
logging = setup_logger('wind_automation', 'wind_automation.log')

def fetch_all_data_wind(plant_name, start_date, current_month_start, last_30_days_start, current_year_start, yearly_date, condition_wind, condition_generation):
    """
    Fetch all required data sequentially for wind DGR generation to reduce RAM usage.
    """
    logging.info(f"Starting sequential wind data fetch for plant: {plant_name}")

    # Fetch data sequentially instead of concurrently
    wind_speed_data_value = fetch_data_total(plant_name, ['WTUR.Wind-Speed'], 'Turbine', start_date, start_date, condition_wind)
    daily_generation_value = fetch_data_total(plant_name, ['WTUR.Generation today'], 'Turbine', start_date, start_date, condition_generation)
    wind_data_monthly_value = fetch_data_total(plant_name, ['WTUR.Wind-Speed'], 'Turbine', current_month_start, start_date, condition_wind)
    generation_monthly_value = fetch_data_total(plant_name, ['WTUR.Generation today'], 'Turbine', current_month_start, start_date, condition_generation)
    wind_data_yearly_value = fetch_data_total(plant_name, ['WTUR.Wind-Speed'], 'Turbine', current_year_start, start_date, condition_wind)
    yearly_generation_value = fetch_data_total(plant_name, ['WTUR.Generation today'], 'Turbine', current_year_start, start_date, condition_generation)
    # wind_data_monthly_plot = fetch_data_total(plant_name, ['WTUR.Wind-Speed'], 'Turbine', last_30_days_start, start_date, condition_wind)
    # generation_monthly_plot = fetch_data_total(plant_name, ['WTUR.Generation today'], 'Turbine', last_30_days_start, start_date, condition_generation)
    # wind_data_yearly_plot = fetch_data_total(plant_name, ['WTUR.Wind-Speed'], 'Turbine', yearly_date, start_date, condition_wind)
    # generation_yearly_plot = fetch_data_total(plant_name, ['WTUR.Generation today'], 'Turbine', yearly_date, start_date, condition_generation)

    logging.info(f"Completed sequential wind data fetch for plant: {plant_name}")

    return {
        "wind_speed_data_value": wind_speed_data_value,
        "daily_generation_value": daily_generation_value,
        "wind_data_monthly_value": wind_data_monthly_value,
        "generation_monthly_value": generation_monthly_value,
        # "wind_data_yearly_value": wind_data_yearly_value,
        # "yearly_generation_value": yearly_generation_value,
        # "wind_data_monthly_plot": wind_data_monthly_plot,
        # "generation_monthly_plot": generation_monthly_plot,
        # "wind_data_yearly_plot": wind_data_yearly_plot,
        # "generation_yearly_plot": generation_yearly_plot,
    }




def generate_wind_automation_report(plant_name, start_date, customer_name, project, ma_percent):
    logging.info(f"Starting wind report generation for Plant: {plant_name}, Date: {start_date}")

    try:
        # Fetch dynamic dates
        current_month_start, _, _, _, _, last_30_days_start, current_year_start, last_year_date = get_dynamic_dates(start_date)
        # yearly_date = '2024-04-01'
        condition_wind = {"Wind-Speed": "mean"}
        condition_generation = {"Generation today": "last"}

        # Fetch all data sequentially
        data = fetch_all_data_wind(plant_name, start_date, current_month_start, last_30_days_start, current_year_start, last_year_date, condition_wind, condition_generation)

        # Extract fetched data
        wind_speed_data_value = data["wind_speed_data_value"]
        daily_generation_value = data["daily_generation_value"]
        wind_data_monthly_value = data["wind_data_monthly_value"]
        generation_monthly_value = data["generation_monthly_value"]
        # wind_data_yearly_value = data["wind_data_yearly_value"]
        # yearly_generation_value = data["yearly_generation_value"]
        # wind_data_monthly_plot = data["wind_data_monthly_plot"]
        # generation_monthly_plot = data["generation_monthly_plot"]
        # wind_data_yearly_plot = data["wind_data_yearly_plot"]
        # generation_yearly_plot = data["generation_yearly_plot"]

        # Perform calculations
        avg_wind_speed = wind_speed_data_value.iloc[:, 1:].mean().mean() if not wind_speed_data_value.empty else 0
        total_generation = daily_generation_value.iloc[:, 1:].sum().sum() if not daily_generation_value.empty else 0
        monthly_wind = wind_data_monthly_value.iloc[:, 1:].mean().mean() if not wind_data_monthly_value.empty else 0
        monthly_generation = generation_monthly_value.iloc[:, 1:].sum().sum() if not generation_monthly_value.empty else 0
        # yearly_wind = wind_data_yearly_value.iloc[:, 1:].mean().mean() if not wind_data_yearly_value.empty else 0
        # yearly_generation = yearly_generation_value.iloc[:, 1:].sum().sum() if not yearly_generation_value.empty else 0

        # Generate plots
        # gen_plot_year = plot_plant_wise_generation_pdf(generation_yearly_plot, plant_name ,date_col="time")
        # wind_plot_year = plot_wind_speed_last_12_months(wind_data_yearly_plot, plant_name,date_col="time")
        # wind_plot_30 = plot_wind_speed_last_30_days(wind_data_monthly_plot, plant_name, date_col="time")
        # gen_plot_30 = plot_daily_generation_last_30_days(generation_monthly_plot, plant_name, date_col="time")
        # combined_30 = generate_wind_plot(wind_plot_30, gen_plot_30, f"{plant_name}_combined_report.pdf")

        # Generate report
        csv_report = generate_dgr_wind_report(
            plant_name ,wind_speed_data_value, daily_generation_value, start_date, customer_name, project, ma_percent
        )

        # capacities = {"IN.INTE.SPFL": "2.7", "IN.INTE.ANSP": "2.7", "IN.INTE.SABE": "1.9"}
        # capacity = capacities.get(plant_name, "N/A")
        capacity = get_capacity_from_csv(plant_name)


        

        final_pdf_path = os.path.join("static", "wind_final_report", f"{plant_name}_DGR_{start_date}.pdf")

        partial_pdf = generate_combined_wind_pdf(plant_name, start_date, customer_name, project, avg_wind_speed, total_generation,
                                                 ma_percent, monthly_wind, monthly_generation, 
                                                 csv_report, capacity, final_pdf_path)
        

        # final_pdf = merge_pdfs_plots(partial_pdf, gen_plot_year, wind_plot_year, combined_30, final_pdf_path)

        # Clean up temporary files
        temp_files = [csv_report]
        for file in temp_files:
            if file and os.path.exists(file):
                os.remove(file)

        # Upload final PDF to S3
        # file_path = f"wind_reports/{plant_name}_DGR_{start_date}.pdf"
        # file_url = upload_file_s3(final_pdf, file_path)

        # logging.info(f"File saved at: {file_url}")
        logging.info(f"Successfully created Wind DGR report at: {final_pdf_path}")

        # Check if data is present in the DB for this plant and date
        result = get_wind_report_data(plant_name, start_date)

        if result:
            print("the data is available")
            data = [{
            "date": start_date,
            "plant_short_name": plant_name,
            "plant_long_name": customer_name,
            "generation": (round(float(total_generation), 2)),
            "wind_speed": round(float(avg_wind_speed), 2),
            "approved": 0,
            "review": 0,
            "action_performed": 0,
            "dgr_path": partial_pdf,
            "status": "Regenerated"
        }]
        else:

            data = [{
                "date": start_date,
                "plant_short_name": plant_name,
                "plant_long_name": customer_name,
                "generation": (round(float(total_generation), 2)),
                "wind_speed": round(float(avg_wind_speed), 2),
                "approved": 0,
                "review": 0,
                "action_performed": 0,
                "dgr_path": partial_pdf
            }]

        # Insert data into database
        insert_wind_data_db(data)
        logging.info(f"Data inserted into database for {plant_name}")



        return partial_pdf

    except Exception as e:
        logging.error(f"Error generating wind report for {plant_name}: {e}", exc_info=True)
        raise
